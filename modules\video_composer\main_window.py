"""
视频合成模块主窗口
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
import json
from datetime import datetime
from .core import VideoComposerProcessor
from .font_manager_ui import show_font_dialog

# 添加common模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from common.path_utils import get_config_dir_path, get_config_path, ensure_dir_exists

class VideoComposerWindow:
    """视频合成窗口类"""
    
    def __init__(self, parent):
        self.parent = parent
        self.processor = VideoComposerProcessor()

        # 创建主框架
        self.frame = tk.Frame(parent, bg="#f8f9fa")
        self.frame.pack(fill=tk.BOTH, expand=True)

        # 文件列表
        self.file_groups = []  # 存储 (video_path, audio_path, subtitle_path, output_name)

        # 输出目录
        self.output_dir = ""

        # 处理状态
        self.is_processing = False
        self.processing_thread = None

        # 计时器相关
        self.start_time = None
        self.timer_running = False

        # 初始化基础视频设置变量（防止保存配置时出错）
        self.resolution_var = tk.StringVar(value="1920x1080")
        self.fps_var = tk.StringVar(value="30")
        self.video_bitrate_var = tk.StringVar(value="2000k")
        self.audio_bitrate_var = tk.StringVar(value="128k")
        self.encoding_speed_var = tk.StringVar(value="ultrafast")  # 固定为极速模式

        # 初始化分步编码器设置变量
        self.subtitle_encoder_var = tk.StringVar(value="auto")
        self.audio_merge_encoder_var = tk.StringVar(value="auto")
        self.final_encoder_var = tk.StringVar(value="auto")

        # 初始化界面
        self.init_ui()

        # 加载设置
        self.load_settings()

        # 确保UI状态正确
        self.on_resolution_enable_changed()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主要布局：左边控制面板，右边输出日志
        main_paned = tk.PanedWindow(self.frame, orient=tk.HORIZONTAL, bg="#f8f9fa")
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧控制面板（增加宽度）
        self.left_frame = tk.Frame(main_paned, bg="#f8f9fa", width=550)
        main_paned.add(self.left_frame, minsize=550)

        # 右侧输出日志（减少最小宽度50像素）
        self.right_frame = tk.Frame(main_paned, bg="#f8f9fa")
        main_paned.add(self.right_frame, minsize=200)

        # 创建左侧控制区域
        self.create_left_panel()

        # 创建右侧日志区域
        self.create_right_panel()

        # 界面创建完成后，延迟初始化硬件状态
        self.frame.after(100, self.init_hardware_status)

    def create_left_panel(self):
        """创建左侧控制面板"""
        # 创建滚动区域
        canvas = tk.Canvas(self.left_frame, bg="#f8f9fa")
        scrollbar = ttk.Scrollbar(self.left_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f8f9fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件到左侧控制面板
        def _on_mousewheel(event):
            try:
                if canvas.winfo_exists():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                pass  # 忽略canvas已销毁的错误

        # 递归绑定滚轮事件到所有子控件
        def bind_mousewheel_to_all_children(widget):
            """递归为所有子控件绑定滚轮事件"""
            try:
                # 为当前控件绑定滚轮事件
                widget.bind("<MouseWheel>", _on_mousewheel)
                # 递归处理所有子控件
                for child in widget.winfo_children():
                    bind_mousewheel_to_all_children(child)
            except tk.TclError:
                pass  # 忽略已销毁的控件

        # 绑定滚轮事件到canvas和scrollable_frame
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)

        # 存储绑定函数以便后续使用
        self._bind_mousewheel_to_children = bind_mousewheel_to_all_children

        # 在滚动框架中创建各个控制区域
        self.create_control_panel(scrollable_frame)
        self.create_bgm_section(scrollable_frame)
        self.create_subtitle_section(scrollable_frame)
        self.create_output_section(scrollable_frame)
        self.create_performance_section(scrollable_frame)
        self.create_video_settings_section(scrollable_frame)
        self.create_progress_section(scrollable_frame)
        self.create_control_section(scrollable_frame)

        # 为所有子控件递归绑定滚轮事件
        self._bind_mousewheel_to_children(scrollable_frame)

    def create_right_panel(self):
        """创建右侧输出日志面板"""
        # 输出日志标题
        log_title = tk.Label(self.right_frame, text="处理日志", font=("微软雅黑", 14, "bold"),
                           bg="#f8f9fa", fg="#495057")
        log_title.pack(pady=(10, 5))

        # 日志文本区域
        log_frame = tk.Frame(self.right_frame, bg="#f8f9fa")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=("Consolas", 12),
                               bg="#ffffff", fg="#333333", relief=tk.SUNKEN, bd=1)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # 布局
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定鼠标滚轮事件到日志文本区域
        def _on_log_mousewheel(event):
            try:
                if self.log_text.winfo_exists():
                    self.log_text.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                pass  # 忽略组件已销毁的错误

        # 绑定滚轮事件到日志文本框
        self.log_text.bind("<MouseWheel>", _on_log_mousewheel)

        # 日志控制按钮
        log_control_frame = tk.Frame(self.right_frame, bg="#f8f9fa")
        log_control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        clear_log_btn = tk.Button(
            log_control_frame,
            text="清空日志",
            font=("微软雅黑", 9),
            bg="#6c757d",
            fg="white",
            activebackground="#545b62",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=3,
            cursor="hand2",
            command=self.clear_log
        )
        clear_log_btn.pack(side=tk.LEFT)

        save_log_btn = tk.Button(
            log_control_frame,
            text="保存日志",
            font=("微软雅黑", 9),
            bg="#17a2b8",
            fg="white",
            activebackground="#138496",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=3,
            cursor="hand2",
            command=self.save_log
        )
        save_log_btn.pack(side=tk.LEFT, padx=(5, 0))

        # 初始化日志
        self.add_log("系统初始化完成，等待处理任务...")
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.LabelFrame(parent, text="控制面板", font=("微软雅黑", 11, "bold"),
                                    bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        control_frame.pack(fill=tk.X, padx=5, pady=3)

        # 处理文件夹选择
        folder_frame = tk.Frame(control_frame, bg="#f8f9fa")
        folder_frame.pack(fill=tk.X, pady=5)

        tk.Label(folder_frame, text="处理文件夹:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        self.input_folder_var = tk.StringVar()
        folder_entry = tk.Entry(folder_frame, textvariable=self.input_folder_var, font=("微软雅黑", 9))
        folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        folder_btn = tk.Button(
            folder_frame,
            text="浏览",
            font=("微软雅黑", 9),
            bg="#007bff",
            fg="white",
            activebackground="#0056b3",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=1,
            cursor="hand2",
            command=self.select_input_folder
        )
        folder_btn.pack(side=tk.RIGHT)

        # 有效子文件夹显示
        subfolder_frame = tk.Frame(control_frame, bg="#f8f9fa")
        subfolder_frame.pack(fill=tk.X, pady=2)

        self.subfolder_label = tk.Label(subfolder_frame, text="有效子文件夹：找到 0 个",
                                       font=("微软雅黑", 9), bg="#f8f9fa", fg="#6c757d")
        self.subfolder_label.pack(side=tk.LEFT)
    
    def create_bgm_section(self, parent):
        """创建BGM设置区域"""
        bgm_frame = tk.LabelFrame(parent, text="添加BGM", font=("微软雅黑", 11, "bold"),
                                bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        bgm_frame.pack(fill=tk.X, padx=5, pady=3)

        # BGM启用开关
        bgm_enable_frame = tk.Frame(bgm_frame, bg="#f8f9fa")
        bgm_enable_frame.pack(fill=tk.X, pady=3)

        self.enable_bgm_var = tk.BooleanVar(value=False)
        self.bgm_enable_check = tk.Checkbutton(bgm_enable_frame, text="启用BGM",
                                              variable=self.enable_bgm_var,
                                              bg="#f8f9fa", font=("微软雅黑", 9),
                                              command=self.on_bgm_enable_changed)
        self.bgm_enable_check.pack(side=tk.LEFT)

        # BGM路径选择
        bgm_path_frame = tk.Frame(bgm_frame, bg="#f8f9fa")
        bgm_path_frame.pack(fill=tk.X, pady=5)

        self.bgm_path_label = tk.Label(bgm_path_frame, text="BGM路径:", font=("微软雅黑", 9), bg="#f8f9fa")
        self.bgm_path_label.pack(side=tk.LEFT)

        self.bgm_path_var = tk.StringVar()
        self.bgm_entry = tk.Entry(bgm_path_frame, textvariable=self.bgm_path_var, font=("微软雅黑", 9), state=tk.DISABLED)
        self.bgm_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.bgm_btn = tk.Button(
            bgm_path_frame,
            text="浏览",
            font=("微软雅黑", 9),
            bg="#007bff",
            fg="white",
            activebackground="#0056b3",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=1,
            cursor="hand2",
            state=tk.DISABLED,
            command=self.select_bgm_file
        )
        self.bgm_btn.pack(side=tk.RIGHT)

        # BGM模式和音量
        bgm_settings_frame = tk.Frame(bgm_frame, bg="#f8f9fa")
        bgm_settings_frame.pack(fill=tk.X, pady=3)

        # BGM模式
        self.bgm_mode_label = tk.Label(bgm_settings_frame, text="BGM模式:", font=("微软雅黑", 9), bg="#f8f9fa", state=tk.DISABLED)
        self.bgm_mode_label.pack(side=tk.LEFT)

        self.bgm_mode_var = tk.StringVar(value="单文件循环")
        bgm_mode_frame = tk.Frame(bgm_settings_frame, bg="#f8f9fa")
        bgm_mode_frame.pack(side=tk.LEFT, padx=(5, 15))

        self.bgm_radio1 = tk.Radiobutton(bgm_mode_frame, text="单文件循环", variable=self.bgm_mode_var,
                                        value="单文件循环", bg="#f8f9fa", font=("微软雅黑", 8),
                                        state=tk.DISABLED, command=self.on_bgm_mode_changed)
        self.bgm_radio1.pack(side=tk.LEFT)

        self.bgm_radio2 = tk.Radiobutton(bgm_mode_frame, text="文件夹随机", variable=self.bgm_mode_var,
                                        value="文件夹随机", bg="#f8f9fa", font=("微软雅黑", 8),
                                        state=tk.DISABLED, command=self.on_bgm_mode_changed)
        self.bgm_radio2.pack(side=tk.LEFT, padx=(5, 0))

        # BGM音量
        self.bgm_volume_label = tk.Label(bgm_settings_frame, text="BGM音量:", font=("微软雅黑", 9), bg="#f8f9fa", state=tk.DISABLED)
        self.bgm_volume_label.pack(side=tk.LEFT)

        self.bgm_volume_var = tk.StringVar(value="15")
        self.bgm_volume_entry = tk.Entry(bgm_settings_frame, textvariable=self.bgm_volume_var, width=6,
                                        font=("微软雅黑", 9), state=tk.DISABLED)
        self.bgm_volume_entry.pack(side=tk.LEFT, padx=(3, 2))

        self.bgm_percent_label = tk.Label(bgm_settings_frame, text="%", font=("微软雅黑", 9), bg="#f8f9fa", state=tk.DISABLED)
        self.bgm_percent_label.pack(side=tk.LEFT)

    def create_subtitle_section(self, parent):
        """创建字幕设置区域"""
        subtitle_frame = tk.LabelFrame(parent, text="添加字幕", font=("微软雅黑", 11, "bold"),
                                     bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        subtitle_frame.pack(fill=tk.X, padx=5, pady=3)

        # 字幕启用开关
        subtitle_enable_frame = tk.Frame(subtitle_frame, bg="#f8f9fa")
        subtitle_enable_frame.pack(fill=tk.X, pady=3)

        self.enable_subtitle_var = tk.BooleanVar(value=False)
        self.subtitle_enable_check = tk.Checkbutton(subtitle_enable_frame, text="启用字幕",
                                                   variable=self.enable_subtitle_var,
                                                   bg="#f8f9fa", font=("微软雅黑", 9),
                                                   command=self.on_subtitle_enable_changed)
        self.subtitle_enable_check.pack(side=tk.LEFT)

        # 字幕样式设置
        style_frame = tk.Frame(subtitle_frame, bg="#f8f9fa")
        style_frame.pack(fill=tk.X, pady=3)

        self.subtitle_style_label = tk.Label(style_frame, text="字幕样式:", font=("微软雅黑", 9), bg="#f8f9fa", state=tk.DISABLED)
        self.subtitle_style_label.pack(side=tk.LEFT)

        self.subtitle_style_var = tk.StringVar(value="微软雅黑 22pt 粗体")
        self.subtitle_style_entry = tk.Entry(style_frame, textvariable=self.subtitle_style_var,
                                            font=("微软雅黑", 9), state=tk.DISABLED)
        self.subtitle_style_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.subtitle_style_btn = tk.Button(
            style_frame,
            text="设置样式",
            font=("微软雅黑", 9),
            bg="#6c757d",
            fg="white",
            activebackground="#545b62",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=1,
            cursor="hand2",
            state=tk.DISABLED,
            command=self.set_subtitle_style
        )
        self.subtitle_style_btn.pack(side=tk.RIGHT)
    
    def create_settings_section(self):
        """创建设置区域"""
        settings_frame = tk.LabelFrame(self.frame, text="合成设置", font=("微软雅黑", 12, "bold"),
                                     bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 第一行：分辨率和帧率
        row1 = tk.Frame(settings_frame, bg="#f8f9fa")
        row1.pack(fill=tk.X, pady=2)
        
        # 分辨率
        tk.Label(row1, text="分辨率:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.resolution_var = tk.StringVar(value="1920x1080")
        resolution_combo = ttk.Combobox(row1, textvariable=self.resolution_var, width=15,
                                       values=["1920x1080", "1280x720", "854x480", "640x360"])
        resolution_combo.pack(side=tk.LEFT, padx=(5, 20))
        resolution_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        # 帧率
        tk.Label(row1, text="帧率:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.fps_var = tk.StringVar(value="30")
        fps_combo = ttk.Combobox(row1, textvariable=self.fps_var, width=10,
                                values=["24", "25", "30", "60"])
        fps_combo.pack(side=tk.LEFT, padx=(5, 0))
        fps_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)
        
        # 第二行：码率设置
        row2 = tk.Frame(settings_frame, bg="#f8f9fa")
        row2.pack(fill=tk.X, pady=2)
        
        # 视频码率
        tk.Label(row2, text="视频码率:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.video_bitrate_var = tk.StringVar(value="2000k")
        video_bitrate_combo = ttk.Combobox(row2, textvariable=self.video_bitrate_var, width=10,
                                          values=["1000k", "2000k", "4000k", "8000k"])
        video_bitrate_combo.pack(side=tk.LEFT, padx=(5, 20))
        video_bitrate_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        # 音频码率
        tk.Label(row2, text="音频码率:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.audio_bitrate_var = tk.StringVar(value="128k")
        audio_bitrate_combo = ttk.Combobox(row2, textvariable=self.audio_bitrate_var, width=10,
                                          values=["96k", "128k", "192k", "256k"])
        audio_bitrate_combo.pack(side=tk.LEFT, padx=(5, 0))
        audio_bitrate_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)
        
        # 第三行：字幕设置
        row3 = tk.Frame(settings_frame, bg="#f8f9fa")
        row3.pack(fill=tk.X, pady=2)
        
        # 字幕字体大小
        tk.Label(row3, text="字幕字体大小:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.subtitle_size_var = tk.StringVar(value="24")
        subtitle_size_entry = tk.Entry(row3, textvariable=self.subtitle_size_var, width=8)
        subtitle_size_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        # 字幕颜色
        tk.Label(row3, text="字幕颜色:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)
        self.subtitle_color_var = tk.StringVar(value="white")
        subtitle_color_combo = ttk.Combobox(row3, textvariable=self.subtitle_color_var, width=10,
                                           values=["white", "black", "red", "green", "blue", "yellow"])
        subtitle_color_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 第四行：FFmpeg状态显示
        row4 = tk.Frame(settings_frame, bg="#f8f9fa")
        row4.pack(fill=tk.X, pady=2)

        # FFmpeg状态
        tk.Label(row4, text="视频引擎:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        # 状态标签
        self.ffmpeg_status_label = tk.Label(row4, text="", font=("微软雅黑", 9), bg="#f8f9fa", fg="#6c757d")
        self.ffmpeg_status_label.pack(side=tk.LEFT, padx=(5, 10))

        # 更新FFmpeg状态
        self.update_ffmpeg_status()
    
    def create_output_section(self, parent):
        """创建输出选项区域"""
        output_frame = tk.LabelFrame(parent, text="输出选项", font=("微软雅黑", 11, "bold"),
                                   bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        output_frame.pack(fill=tk.X, padx=5, pady=3)

        # 合并功能开关
        self.enable_merge_var = tk.BooleanVar(value=True)
        merge_enable_frame = tk.Frame(output_frame, bg="#f8f9fa")
        merge_enable_frame.pack(fill=tk.X, pady=3)

        self.merge_checkbox = tk.Checkbutton(
            merge_enable_frame,
            text="启用合并功能",
            variable=self.enable_merge_var,
            bg="#f8f9fa",
            font=("微软雅黑", 9, "bold"),
            fg="#007bff",
            command=self.toggle_merge_options
        )
        self.merge_checkbox.pack(anchor="w")

        # 合并模式选择框架
        self.merge_options_frame = tk.Frame(output_frame, bg="#f8f9fa")
        self.merge_options_frame.pack(fill=tk.X, pady=(0, 3))

        # 输出模式选择
        self.merge_mode_var = tk.StringVar(value="create_merged")

        merge_frame = tk.Frame(self.merge_options_frame, bg="#f8f9fa")
        merge_frame.pack(fill=tk.X, padx=20)  # 缩进显示

        self.merge_radio1 = tk.Radiobutton(merge_frame, text="创建合并视频（将所有片段合并为单个视频）",
                      variable=self.merge_mode_var, value="create_merged",
                      bg="#f8f9fa", font=("微软雅黑", 8))
        self.merge_radio1.pack(anchor="w")

        self.merge_radio2 = tk.Radiobutton(merge_frame, text="合并原视频与第一集（将原视频与1号音频处理视频合并）",
                      variable=self.merge_mode_var, value="merge_with_first",
                      bg="#f8f9fa", font=("微软雅黑", 8))
        self.merge_radio2.pack(anchor="w", pady=(2, 0))

        # 添加说明文本
        info_frame = tk.Frame(self.merge_options_frame, bg="#f8f9fa")
        info_frame.pack(fill=tk.X, padx=20, pady=(5, 0))

        info_label = tk.Label(
            info_frame,
            text="💡 关闭合并功能时：一个音频对应一个视频文件",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg="#f8f9fa"
        )
        info_label.pack(anchor="w")

        # 输出目录选择
        dir_frame = tk.Frame(output_frame, bg="#f8f9fa")
        dir_frame.pack(fill=tk.X, pady=(5, 3))

        tk.Label(dir_frame, text="输出目录:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        self.output_dir_var = tk.StringVar()
        dir_entry = tk.Entry(dir_frame, textvariable=self.output_dir_var, font=("微软雅黑", 9))
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        dir_btn = tk.Button(
            dir_frame,
            text="浏览",
            font=("微软雅黑", 9),
            bg="#6c757d",
            fg="white",
            activebackground="#545b62",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=1,
            cursor="hand2",
            command=self.select_output_dir
        )
        dir_btn.pack(side=tk.RIGHT)

        # 初始化合并选项状态
        self.toggle_merge_options()

    def toggle_merge_options(self):
        """切换合并选项的显示状态"""
        if self.enable_merge_var.get():
            # 启用合并功能 - 显示合并选项
            self.merge_options_frame.pack(fill=tk.X, pady=(0, 3))
            # 启用单选按钮
            self.merge_radio1.config(state=tk.NORMAL)
            self.merge_radio2.config(state=tk.NORMAL)
        else:
            # 禁用合并功能 - 隐藏合并选项
            self.merge_options_frame.pack_forget()
            # 禁用单选按钮
            self.merge_radio1.config(state=tk.DISABLED)
            self.merge_radio2.config(state=tk.DISABLED)

    def create_performance_section(self, parent):
        """创建性能选项区域"""
        perf_frame = tk.LabelFrame(parent, text="性能设置", font=("微软雅黑", 11, "bold"),
                                 bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        perf_frame.pack(fill=tk.X, padx=5, pady=3)

        # FFmpeg状态显示
        status_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        status_frame.pack(fill=tk.X, pady=3)

        tk.Label(status_frame, text="视频合成器:", font=("微软雅黑", 9, "bold"), bg="#f8f9fa").pack(side=tk.LEFT)

        self.composer_status_label = tk.Label(status_frame, text="", font=("微软雅黑", 9),
                                            bg="#f8f9fa", fg="#28a745")
        self.composer_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 更新状态显示
        self.update_ffmpeg_status()

        # 并行处理选项
        parallel_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        parallel_frame.pack(fill=tk.X, pady=3)

        self.enable_parallel_var = tk.BooleanVar(value=True)
        parallel_check = tk.Checkbutton(parallel_frame, text="启用并行处理",
                                       variable=self.enable_parallel_var,
                                       bg="#f8f9fa", font=("微软雅黑", 9, "bold"),
                                       fg="#007bff",
                                       command=self.on_video_setting_changed)
        parallel_check.pack(side=tk.LEFT)

        # 并行处理说明
        parallel_info = tk.Label(parallel_frame, text="(多集同时处理，速度提升2-4倍)",
                               font=("微软雅黑", 8), bg="#f8f9fa", fg="#28a745")
        parallel_info.pack(side=tk.LEFT, padx=(5, 0))

        # 最大线程数
        thread_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        thread_frame.pack(fill=tk.X, pady=3)

        tk.Label(thread_frame, text="最大线程数:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        self.max_threads_var = tk.StringVar(value="4")
        thread_entry = tk.Entry(thread_frame, textvariable=self.max_threads_var, width=6, font=("微软雅黑", 9))
        thread_entry.pack(side=tk.LEFT, padx=(5, 5))

        # 绑定变更事件，自动保存配置
        thread_entry.bind('<KeyRelease>', self.on_video_setting_changed)
        thread_entry.bind('<FocusOut>', self.on_video_setting_changed)

        # 获取CPU核心数
        import os
        cpu_count = os.cpu_count() or 4

        tk.Label(thread_frame, text=f"（建议2-{cpu_count}，当前CPU: {cpu_count}核）", font=("微软雅黑", 8),
                bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT)

        # 快速模式选项（内部使用）
        self.fast_mode_var = tk.BooleanVar(value=True)

        # 速度优先模式选项（新增）
        self.speed_priority_var = tk.BooleanVar(value=True)

        # 硬件加速选项
        self.use_hardware_acceleration_var = tk.BooleanVar(value=True)

        # 硬件加速复选框
        hardware_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        hardware_frame.pack(fill=tk.X, pady=2)

        hardware_check = tk.Checkbutton(
            hardware_frame,
            text="启用硬件加速 (NVENC/QSV/AMF)",
            variable=self.use_hardware_acceleration_var,
            font=("微软雅黑", 9),
            bg="#f8f9fa",
            fg="#495057",
            activebackground="#f8f9fa",
            command=self.on_hardware_acceleration_changed
        )
        hardware_check.pack(side=tk.LEFT)

        # 硬件加速检测按钮
        detect_btn = tk.Button(hardware_frame, text="检测硬件",
                              font=("微软雅黑", 8), bg="#17a2b8", fg="white",
                              relief=tk.FLAT, cursor="hand2", padx=8, pady=2,
                              command=self.detect_hardware_acceleration)
        detect_btn.pack(side=tk.LEFT, padx=(10, 0))

        # 硬件加速状态标签
        self.hardware_status_label = tk.Label(
            hardware_frame,
            text="未检测",
            font=("微软雅黑", 8),
            bg="#f8f9fa",
            fg="#6c757d"
        )
        self.hardware_status_label.pack(side=tk.LEFT, padx=(5, 0))

        # 速度优先模式选项
        speed_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        speed_frame.pack(fill=tk.X, pady=2)

        speed_check = tk.Checkbutton(
            speed_frame,
            text="🚀 速度优先模式 (最大化编码速度)",
            variable=self.speed_priority_var,
            font=("微软雅黑", 9),
            bg="#f8f9fa",
            fg="#495057",
            activebackground="#f8f9fa",
            command=self.on_speed_priority_changed
        )
        speed_check.pack(side=tk.LEFT)

        # 速度优先说明标签
        speed_info_label = tk.Label(
            speed_frame,
            text="(牺牲少量质量换取最快编码速度)",
            font=("微软雅黑", 8),
            bg="#f8f9fa",
            fg="#6c757d"
        )
        speed_info_label.pack(side=tk.LEFT, padx=(5, 0))



        # 硬件状态将在界面完全初始化后进行检测

        # 字幕处理已默认优化（强制软件编码+多线程）

        # 性能优化状态标签（移除按钮，改为状态显示）
        optimize_frame = tk.Frame(perf_frame, bg="#f8f9fa")
        optimize_frame.pack(fill=tk.X, pady=5)

        # 优化状态标签
        self.optimization_result_label = tk.Label(
            optimize_frame,
            text="🔧 正在进行性能优化...",
            font=("微软雅黑", 9),
            bg="#f8f9fa",
            fg="#007bff"
        )
        self.optimization_result_label.pack(side=tk.LEFT)



    def create_video_settings_section(self, parent):
        """创建视频设置区域"""
        video_frame = tk.LabelFrame(parent, text="视频设置", font=("微软雅黑", 11, "bold"),
                                  bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        video_frame.pack(fill=tk.X, padx=5, pady=3)

        # 自定义分辨率
        resolution_frame = tk.Frame(video_frame, bg="#f8f9fa")
        resolution_frame.pack(fill=tk.X, pady=3)

        self.custom_resolution_var = tk.BooleanVar(value=False)
        self.resolution_check = tk.Checkbutton(resolution_frame, text="自定义分辨率",
                                              variable=self.custom_resolution_var,
                                              bg="#f8f9fa", font=("微软雅黑", 9),
                                              command=self.on_resolution_enable_changed)
        self.resolution_check.pack(side=tk.LEFT)

        # 宽度和高度
        size_frame = tk.Frame(video_frame, bg="#f8f9fa")
        size_frame.pack(fill=tk.X, pady=3)

        self.width_label = tk.Label(size_frame, text="宽:", font=("微软雅黑", 9), bg="#f8f9fa")
        self.width_label.pack(side=tk.LEFT)

        self.width_var = tk.StringVar(value="960")
        self.width_entry = tk.Entry(size_frame, textvariable=self.width_var, width=6, font=("微软雅黑", 9))
        self.width_entry.pack(side=tk.LEFT, padx=(3, 10))
        self.width_entry.bind('<KeyRelease>', self.on_video_setting_changed)

        self.height_label = tk.Label(size_frame, text="高:", font=("微软雅黑", 9), bg="#f8f9fa")
        self.height_label.pack(side=tk.LEFT)

        self.height_var = tk.StringVar(value="720")
        self.height_entry = tk.Entry(size_frame, textvariable=self.height_var, width=6, font=("微软雅黑", 9))
        self.height_entry.pack(side=tk.LEFT, padx=(3, 0))
        self.height_entry.bind('<KeyRelease>', self.on_video_setting_changed)

        # 视频压缩设置区域
        compression_section_frame = tk.LabelFrame(video_frame, text="视频压缩设置", font=("微软雅黑", 10, "bold"),
                                                 bg="#f8f9fa", fg="#495057", padx=8, pady=6)
        compression_section_frame.pack(fill=tk.X, pady=3)

        # 压缩开关
        compression_enable_frame = tk.Frame(compression_section_frame, bg="#f8f9fa")
        compression_enable_frame.pack(fill=tk.X, pady=2)

        self.custom_compression_var = tk.BooleanVar(value=True)
        self.compression_check = tk.Checkbutton(compression_enable_frame, text="启用自定义压缩",
                                               variable=self.custom_compression_var,
                                               bg="#f8f9fa", font=("微软雅黑", 9, "bold"),
                                               fg="#007bff",
                                               command=self.on_compression_enable_changed)
        self.compression_check.pack(side=tk.LEFT)

        # 压缩说明
        compression_info_frame = tk.Frame(compression_section_frame, bg="#f8f9fa")
        compression_info_frame.pack(fill=tk.X, pady=(0, 5))

        info_text = tk.Text(compression_info_frame, height=3, font=("微软雅黑", 8),
                           bg="#e9ecef", fg="#495057", relief=tk.FLAT, wrap=tk.WORD)
        info_text.pack(fill=tk.X, padx=5)

        info_content = """码率设置说明：
• 码率越高 = 质量越好 = 文件越大 | 码率越低 = 质量越低 = 文件越小
• 5000k+: 高质量(推荐首集) | 3000-4999k: 中等质量 | 1500-2999k: 较低质量 | <1500k: 低质量"""

        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)

        # 压缩设置容器
        self.compression_settings_frame = tk.Frame(compression_section_frame, bg="#f8f9fa")
        self.compression_settings_frame.pack(fill=tk.X, pady=2)

        # 第一集码率设置
        first_rate_frame = tk.Frame(self.compression_settings_frame, bg="#f8f9fa")
        first_rate_frame.pack(fill=tk.X, pady=2)

        self.first_compression_label = tk.Label(first_rate_frame, text="第一集码率:", font=("微软雅黑", 9), bg="#f8f9fa")
        self.first_compression_label.pack(side=tk.LEFT)

        self.first_compression_var = tk.StringVar(value="5000")
        self.first_compression_entry = tk.Entry(first_rate_frame, textvariable=self.first_compression_var, width=8, font=("微软雅黑", 9))
        self.first_compression_entry.pack(side=tk.LEFT, padx=(5, 2))
        self.first_compression_entry.bind('<KeyRelease>', self.on_video_setting_changed)

        tk.Label(first_rate_frame, text="k ", font=("微软雅黑", 8), bg="#f8f9fa").pack(side=tk.LEFT)

        # 第一集质量指示器
        self.first_quality_label = tk.Label(first_rate_frame, text="高质量", font=("微软雅黑", 8, "bold"),
                                           bg="#f8f9fa", fg="#28a745")
        self.first_quality_label.pack(side=tk.LEFT, padx=(5, 0))

        # 其他集码率设置
        other_rate_frame = tk.Frame(self.compression_settings_frame, bg="#f8f9fa")
        other_rate_frame.pack(fill=tk.X, pady=2)

        self.other_compression_label = tk.Label(other_rate_frame, text="其他集码率:", font=("微软雅黑", 9), bg="#f8f9fa")
        self.other_compression_label.pack(side=tk.LEFT)

        self.other_compression_var = tk.StringVar(value="3000")
        self.other_compression_entry = tk.Entry(other_rate_frame, textvariable=self.other_compression_var, width=8, font=("微软雅黑", 9))
        self.other_compression_entry.pack(side=tk.LEFT, padx=(5, 2))
        self.other_compression_entry.bind('<KeyRelease>', self.on_video_setting_changed)

        tk.Label(other_rate_frame, text="k ", font=("微软雅黑", 8), bg="#f8f9fa").pack(side=tk.LEFT)

        # 其他集质量指示器
        self.other_quality_label = tk.Label(other_rate_frame, text="中等质量", font=("微软雅黑", 8, "bold"),
                                           bg="#f8f9fa", fg="#ffc107")
        self.other_quality_label.pack(side=tk.LEFT, padx=(5, 0))

        # 绑定实时更新质量指示器
        self.first_compression_var.trace('w', self.update_quality_indicators)
        self.other_compression_var.trace('w', self.update_quality_indicators)

        # 输出格式设置
        format_frame = tk.Frame(video_frame, bg="#f8f9fa")
        format_frame.pack(fill=tk.X, pady=3)

        tk.Label(format_frame, text="输出格式:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        self.output_format_var = tk.StringVar(value="mp4")
        format_combo = ttk.Combobox(format_frame, textvariable=self.output_format_var, width=8,
                                   values=["mp4", "avi", "mkv", "mov"], state="readonly")
        format_combo.pack(side=tk.LEFT, padx=(5, 10))
        format_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        # 分步编码器设置区域
        encoder_section_frame = tk.LabelFrame(video_frame, text="分步编码器设置", font=("微软雅黑", 10, "bold"),
                                            bg="#f8f9fa", fg="#495057", padx=8, pady=6)
        encoder_section_frame.pack(fill=tk.X, pady=(5, 0))

        # 字幕合成编码器
        subtitle_encoder_frame = tk.Frame(encoder_section_frame, bg="#f8f9fa")
        subtitle_encoder_frame.pack(fill=tk.X, pady=2)

        tk.Label(subtitle_encoder_frame, text="字幕合成:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        # 不重新初始化，使用已有的变量
        subtitle_encoder_combo = ttk.Combobox(subtitle_encoder_frame, textvariable=self.subtitle_encoder_var, width=12,
                                            values=["auto", "libx264", "h264_nvenc", "h264_qsv", "h264_amf"], state="readonly")
        subtitle_encoder_combo.pack(side=tk.LEFT, padx=(5, 10))
        subtitle_encoder_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        tk.Label(subtitle_encoder_frame, text="(添加字幕到视频)", font=("微软雅黑", 8), bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT)

        # 音频合并编码器
        audio_merge_encoder_frame = tk.Frame(encoder_section_frame, bg="#f8f9fa")
        audio_merge_encoder_frame.pack(fill=tk.X, pady=2)

        tk.Label(audio_merge_encoder_frame, text="音频合并:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        # 不重新初始化，使用已有的变量
        audio_merge_encoder_combo = ttk.Combobox(audio_merge_encoder_frame, textvariable=self.audio_merge_encoder_var, width=12,
                                               values=["auto", "libx264", "h264_nvenc", "h264_qsv", "h264_amf"], state="readonly")
        audio_merge_encoder_combo.pack(side=tk.LEFT, padx=(5, 10))
        audio_merge_encoder_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        tk.Label(audio_merge_encoder_frame, text="(合并音频和BGM)", font=("微软雅黑", 8), bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT)

        # 视频输出编码器
        final_encoder_frame = tk.Frame(encoder_section_frame, bg="#f8f9fa")
        final_encoder_frame.pack(fill=tk.X, pady=2)

        tk.Label(final_encoder_frame, text="视频输出:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)

        # 不重新初始化，使用已有的变量
        final_encoder_combo = ttk.Combobox(final_encoder_frame, textvariable=self.final_encoder_var, width=12,
                                         values=["auto", "libx264", "h264_nvenc", "h264_qsv", "h264_amf"], state="readonly")
        final_encoder_combo.pack(side=tk.LEFT, padx=(5, 10))
        final_encoder_combo.bind('<<ComboboxSelected>>', self.on_video_setting_changed)

        tk.Label(final_encoder_frame, text="(视频编码和输出)", font=("微软雅黑", 8), bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT)

        # 一键优化按钮（移除性能测试按钮）
        encoder_optimize_frame = tk.Frame(encoder_section_frame, bg="#f8f9fa")
        encoder_optimize_frame.pack(fill=tk.X, pady=(5, 0))
        optimize_btn = tk.Button(
            encoder_optimize_frame,
            text="一键优化设置",
            font=("微软雅黑", 9),
            bg="#28a745",
            fg="white",
            activebackground="#218838",
            activeforeground="white",
            relief=tk.FLAT,
            padx=15,
            pady=4,
            cursor="hand2",
            command=self.optimize_encoder_settings
        )
        optimize_btn.pack(side=tk.LEFT)

        # 优化结果显示标签
        self.test_result_label = tk.Label(encoder_optimize_frame, text="", font=("微软雅黑", 8),
                                         bg="#f8f9fa", fg="#6c757d")
        self.test_result_label.pack(side=tk.LEFT, padx=(10, 0))

        # 编码速度已固定为ultrafast（极速模式）


    
    def create_progress_section(self, parent):
        """创建进度显示区域"""
        progress_frame = tk.LabelFrame(parent, text="处理进度", font=("微软雅黑", 11, "bold"),
                                     bg="#f8f9fa", fg="#495057", padx=8, pady=8)
        progress_frame.pack(fill=tk.X, padx=5, pady=3)

        # 总体进度
        overall_frame = tk.Frame(progress_frame, bg="#f8f9fa")
        overall_frame.pack(fill=tk.X, pady=2)

        tk.Label(overall_frame, text="总体进度:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)
        self.overall_progress = ttk.Progressbar(overall_frame, mode='determinate')
        self.overall_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        self.overall_label = tk.Label(overall_frame, text="0/0 (0%)", font=("微软雅黑", 9), bg="#f8f9fa")
        self.overall_label.pack(side=tk.RIGHT)

        # 当前文件和计时器
        current_frame = tk.Frame(progress_frame, bg="#f8f9fa")
        current_frame.pack(fill=tk.X, pady=2)

        tk.Label(current_frame, text="当前文件:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)
        self.current_file_label = tk.Label(current_frame, text="无", font=("微软雅黑", 9), bg="#f8f9fa")
        self.current_file_label.pack(side=tk.LEFT, padx=(5, 0))

        # 计时器（右对齐）
        timer_frame = tk.Frame(current_frame, bg="#f8f9fa")
        timer_frame.pack(side=tk.RIGHT)

        tk.Label(timer_frame, text="用时:", font=("微软雅黑", 9), bg="#f8f9fa").pack(side=tk.LEFT)
        self.timer_label = tk.Label(timer_frame, text="00:00:00", font=("微软雅黑", 9, "bold"),
                                   bg="#f8f9fa", fg="#007bff")
        self.timer_label.pack(side=tk.LEFT, padx=(5, 0))

        # 状态信息
        self.status_label = tk.Label(progress_frame, text="就绪", font=("微软雅黑", 9),
                                   bg="#f8f9fa", fg="#28a745")
        self.status_label.pack(pady=3)
    
    def create_control_section(self, parent):
        """创建控制按钮区域"""
        control_frame = tk.Frame(parent, bg="#f8f9fa")
        control_frame.pack(fill=tk.X, padx=5, pady=8)

        # 开始处理按钮
        self.start_btn = tk.Button(
            control_frame,
            text="开始处理",
            font=("微软雅黑", 10, "bold"),
            bg="#28a745",
            fg="white",
            activebackground="#218838",
            activeforeground="white",
            relief=tk.FLAT,
            padx=20,
            pady=6,
            cursor="hand2",
            command=self.start_processing
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 8))

        # 停止处理按钮
        self.stop_btn = tk.Button(
            control_frame,
            text="取消",
            font=("微软雅黑", 10, "bold"),
            bg="#dc3545",
            fg="white",
            activebackground="#c82333",
            activeforeground="white",
            relief=tk.FLAT,
            padx=20,
            pady=6,
            cursor="hand2",
            state=tk.DISABLED,
            command=self.stop_processing
        )
        self.stop_btn.pack(side=tk.LEFT)
    
    def load_settings(self):
        """加载设置"""
        # 初始化有效子文件夹列表
        self.valid_subfolders = []

        # 加载视频合成配置（包括输出目录）
        self.load_video_composer_config()

    def load_video_composer_config(self):
        """加载视频合成配置"""
        try:
            config_dir = get_config_dir_path()
            config_file = os.path.join(config_dir, "video_composer_config.json")

            # 设置默认输出目录
            default_output = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "output")
            if not os.path.exists(default_output):
                os.makedirs(default_output, exist_ok=True)

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载基础视频设置
                if 'resolution' in config:
                    self.resolution_var.set(config['resolution'])

                if 'fps' in config:
                    self.fps_var.set(config['fps'])

                if 'video_bitrate' in config:
                    self.video_bitrate_var.set(config['video_bitrate'])

                if 'audio_bitrate' in config:
                    self.audio_bitrate_var.set(config['audio_bitrate'])

                # 加载字幕样式设置
                if 'subtitle_style' in config:
                    self.subtitle_style_var.set(config['subtitle_style'])

                # 加载BGM设置
                if 'enable_bgm' in config:
                    self.enable_bgm_var.set(config['enable_bgm'])
                    self.on_bgm_enable_changed()

                if 'bgm_mode' in config:
                    self.bgm_mode_var.set(config['bgm_mode'])

                if 'bgm_volume' in config:
                    self.bgm_volume_var.set(str(config['bgm_volume']))

                # 加载字幕设置
                if 'enable_subtitle' in config:
                    self.enable_subtitle_var.set(config['enable_subtitle'])
                    self.on_subtitle_enable_changed()

                # 加载其他设置
                if 'enable_merge' in config:
                    self.enable_merge_var.set(config['enable_merge'])
                    self.toggle_merge_options()  # 更新界面状态

                if 'merge_mode' in config:
                    self.merge_mode_var.set(config['merge_mode'])

                if 'enable_parallel' in config:
                    self.enable_parallel_var.set(config['enable_parallel'])

                if 'max_threads' in config:
                    self.max_threads_var.set(str(config['max_threads']))

                # 注释：现在允许用户自定义编码速度

                # 加载硬件加速设置
                if 'use_hardware_acceleration' in config:
                    self.use_hardware_acceleration_var.set(config['use_hardware_acceleration'])

                if 'custom_resolution' in config:
                    self.custom_resolution_var.set(config['custom_resolution'])
                    self.on_resolution_enable_changed()  # 更新UI状态

                if 'width' in config:
                    self.width_var.set(str(config['width']))

                if 'height' in config:
                    self.height_var.set(str(config['height']))

                if 'custom_compression' in config:
                    self.custom_compression_var.set(config['custom_compression'])
                    self.on_compression_enable_changed()

                # 兼容旧配置格式（百分比）和新配置格式（码率）
                if 'first_compression_bitrate' in config:
                    self.first_compression_var.set(str(config['first_compression_bitrate']))
                elif 'first_compression_ratio' in config:
                    # 兼容旧配置：将百分比转换为码率
                    old_ratio = config['first_compression_ratio']
                    if old_ratio >= 90:
                        bitrate = 5000
                    elif old_ratio >= 70:
                        bitrate = 3000
                    elif old_ratio >= 50:
                        bitrate = 2000
                    else:
                        bitrate = 1000
                    self.first_compression_var.set(str(bitrate))

                if 'other_compression_bitrate' in config:
                    self.other_compression_var.set(str(config['other_compression_bitrate']))
                elif 'other_compression_ratio' in config:
                    # 兼容旧配置：将百分比转换为码率
                    old_ratio = config['other_compression_ratio']
                    if old_ratio >= 90:
                        bitrate = 5000
                    elif old_ratio >= 70:
                        bitrate = 3000
                    elif old_ratio >= 50:
                        bitrate = 2000
                    else:
                        bitrate = 1000
                    self.other_compression_var.set(str(bitrate))

                # 加载新的视频设置
                if 'output_format' in config:
                    self.output_format_var.set(config['output_format'])

                # 加载输出目录设置
                if 'output_dir' in config and config['output_dir']:
                    self.output_dir = config['output_dir']
                    self.output_dir_var.set(config['output_dir'])
                else:
                    # 使用默认输出目录
                    self.output_dir = default_output
                    self.output_dir_var.set(default_output)

                # 加载分步编码器设置
                if 'subtitle_encoder' in config:
                    self.subtitle_encoder_var.set(config['subtitle_encoder'])

                if 'audio_merge_encoder' in config:
                    self.audio_merge_encoder_var.set(config['audio_merge_encoder'])

                if 'final_encoder' in config:
                    self.final_encoder_var.set(config['final_encoder'])

                # 编码速度固定为ultrafast（极速模式）
                self.encoding_speed_var.set("ultrafast")

                # 加载速度优先设置
                if 'speed_priority' in config:
                    self.speed_priority_var.set(config['speed_priority'])

                # 检查是否需要进行性能优化
                performance_optimized = config.get('performance_optimized', False)
                self._performance_optimized = performance_optimized

                if not performance_optimized:
                    # 第一次使用，需要进行性能优化
                    self.add_log("检测到首次使用，将进行性能优化...")
                    self.frame.after(1000, self.auto_performance_optimization)
                else:
                    # 已经优化过，显示状态
                    self.optimization_result_label.config(
                        text="✅ 性能已优化", fg="#28a745"
                    )

                self.add_log("已加载视频合成配置")

                # 初始化硬件加速状态
                self.frame.after(500, self.init_hardware_status)
            else:
                # 配置文件不存在，使用默认输出目录
                self.output_dir = default_output
                self.output_dir_var.set(default_output)
                # 标记为未优化，需要进行首次优化
                self._performance_optimized = False
                self.add_log("检测到首次使用，将进行性能优化...")
                self.frame.after(1000, self.auto_performance_optimization)
                # 初始化硬件加速状态
                self.frame.after(500, self.init_hardware_status)

        except Exception as e:
            self.add_log(f"加载配置失败: {str(e)}")
            # 即使配置加载失败，也要设置默认输出目录
            self.output_dir = default_output
            self.output_dir_var.set(default_output)
            # 标记为未优化，需要进行首次优化
            self._performance_optimized = False
            self.add_log("配置加载失败，将进行性能优化...")
            self.frame.after(1000, self.auto_performance_optimization)
            # 即使配置加载失败，也要初始化硬件状态
            self.frame.after(500, self.init_hardware_status)

    def save_video_composer_config(self):
        """保存视频合成配置"""
        try:
            config_dir = get_config_dir_path()
            ensure_dir_exists(config_dir)

            config_file = os.path.join(config_dir, "video_composer_config.json")

            config = {
                # 输出目录设置
                'output_dir': self.output_dir,

                # 基础视频设置
                'resolution': self.resolution_var.get(),
                'fps': self.fps_var.get(),
                'video_bitrate': self.video_bitrate_var.get(),
                'audio_bitrate': self.audio_bitrate_var.get(),

                # 字幕和BGM设置
                'subtitle_style': self.subtitle_style_var.get(),
                'enable_bgm': self.enable_bgm_var.get(),
                'bgm_mode': self.bgm_mode_var.get(),
                'bgm_volume': int(self.bgm_volume_var.get()) if self.bgm_volume_var.get().isdigit() else 15,
                'enable_subtitle': self.enable_subtitle_var.get(),

                # 合成设置
                'enable_merge': self.enable_merge_var.get(),
                'merge_mode': self.merge_mode_var.get(),
                'enable_parallel': self.enable_parallel_var.get(),
                'max_threads': int(self.max_threads_var.get()) if self.max_threads_var.get().isdigit() else 4,
                'use_hardware_acceleration': self.use_hardware_acceleration_var.get(),

                # 自定义分辨率设置
                'custom_resolution': self.custom_resolution_var.get(),
                'width': int(self.width_var.get()) if self.width_var.get().isdigit() else 960,
                'height': int(self.height_var.get()) if self.height_var.get().isdigit() else 720,

                # 压缩设置
                'custom_compression': self.custom_compression_var.get(),
                'first_compression_bitrate': int(self.first_compression_var.get()) if self.first_compression_var.get().isdigit() else 5000,
                'other_compression_bitrate': int(self.other_compression_var.get()) if self.other_compression_var.get().isdigit() else 3000,

                # 编码设置
                'output_format': self.output_format_var.get(),
                'encoding_speed': self.encoding_speed_var.get(),
                'speed_priority': self.speed_priority_var.get(),  # 新增：速度优先模式
                'performance_optimized': getattr(self, '_performance_optimized', False),  # 性能优化标志

                # 分步编码器设置
                'subtitle_encoder': self.subtitle_encoder_var.get(),
                'audio_merge_encoder': self.audio_merge_encoder_var.get(),
                'final_encoder': self.final_encoder_var.get(),

                'last_saved': datetime.now().isoformat()
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.add_log("视频合成配置已保存")

        except Exception as e:
            self.add_log(f"保存配置失败: {str(e)}")

    def add_log(self, message):
        """添加日志信息"""
        # 安全检查：确保log_text已经创建
        if not hasattr(self, 'log_text'):
            print(f"[LOG] {message}")  # 如果log_text还没创建，输出到控制台
            return

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def update_progress_bar(self, message, progress_percent=None):
        """更新进度条显示（在同一行刷新）"""
        # 安全检查：确保log_text已经创建
        if not hasattr(self, 'log_text'):
            print(f"[PROGRESS] {message} {progress_percent}%" if progress_percent else f"[PROGRESS] {message}")
            return

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 如果有百分比，创建进度条
        if progress_percent is not None:
            # 创建进度条字符串
            bar_length = 25  # 稍微缩短进度条为更多文本留空间
            filled_length = int(bar_length * progress_percent / 100)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)

            # 检查消息中是否包含详细信息（如剩余时间）
            if " - " in message and "(" in message:
                # 消息格式：导出进度: 85% - 正在处理视频帧 (剩余约2分30秒)
                progress_text = f"[{timestamp}] {message} {bar}"
            else:
                # 简单格式
                progress_text = f"[{timestamp}] {message} {bar} {progress_percent}%"
        else:
            progress_text = f"[{timestamp}] {message}"

        self.log_text.config(state=tk.NORMAL)

        # 获取最后一行的内容
        last_line_start = self.log_text.index("end-1c linestart")
        last_line_end = self.log_text.index("end-1c")
        last_line_content = self.log_text.get(last_line_start, last_line_end)

        # 如果最后一行是进度信息，则替换它；否则添加新行
        progress_keywords = [
            "导出进度:", "视频处理进度:", "处理进度:", "视频渲染:", "字幕处理进度:", "合成进度:",
            "分辨率调整:", "最终编码:", "重新编码合并:", "修复合并", "filter合并:", "拼接视频:",
            "重新编码拼接:", "音视频合并:", "字幕添加:", "创建背景视频:", "生成最终视频:"
        ]
        is_last_line_progress = any(keyword in last_line_content for keyword in progress_keywords)

        if is_last_line_progress:
            # 替换最后一行
            self.log_text.delete(last_line_start, last_line_end)
            self.log_text.insert(last_line_start, progress_text)
        else:
            # 添加新行
            self.log_text.insert(tk.END, progress_text + "\n")

        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.add_log("日志已清空")

    def save_log(self):
        """保存日志到文件"""
        from tkinter import filedialog
        file_path = filedialog.asksaveasfilename(
            title="保存日志文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.add_log(f"日志已保存到: {file_path}")
                messagebox.showinfo("成功", "日志保存成功！")
            except Exception as e:
                self.add_log(f"保存日志失败: {str(e)}")
                messagebox.showerror("错误", f"保存日志失败：{str(e)}")

    def select_input_folder(self):
        """选择输入文件夹"""
        folder_path = filedialog.askdirectory(title="选择处理文件夹")
        if folder_path:
            self.input_folder_var.set(folder_path)
            self.scan_subfolders(folder_path)

    def scan_subfolders(self, folder_path):
        """扫描小说项目文件夹"""
        try:
            self.add_log(f"开始扫描视频素材文件夹: {folder_path}")
            novel_projects = []

            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    # 扫描小说项目中的媒体文件
                    media_info = self.scan_project_media(item_path)
                    if media_info['has_media']:
                        novel_projects.append({
                            'name': item,
                            'path': item_path,
                            'media_info': media_info
                        })

                        # 详细的项目信息
                        status_parts = []
                        if media_info['has_original']:
                            status_parts.append("有原视频")
                        if media_info['has_loop']:
                            status_parts.append("有循环视频")
                        if media_info['episode_count'] > 0:
                            status_parts.append(f"{media_info['episode_count']}集")

                        status_str = ", ".join(status_parts) if status_parts else "文件不完整"

                        if media_info['missing_files']:
                            missing_str = ", ".join(media_info['missing_files'][:3])  # 只显示前3个
                            if len(media_info['missing_files']) > 3:
                                missing_str += "..."
                            self.add_log(f"找到小说项目: {item} ({status_str}) - 缺少: {missing_str}")
                        else:
                            self.add_log(f"找到小说项目: {item} ({status_str}) - 文件完整")

            result_text = f"有效子文件夹：找到 {len(novel_projects)} 个小说项目"
            self.subfolder_label.config(text=result_text)
            self.add_log(result_text)
            self.valid_subfolders = novel_projects

            if novel_projects:
                self.add_log("小说项目列表:")
                for i, project in enumerate(novel_projects, 1):
                    self.add_log(f"  {i}. {project['name']}")

        except Exception as e:
            error_msg = f"扫描文件夹时出错: {str(e)}"
            self.subfolder_label.config(text="扫描文件夹时出错")
            self.add_log(error_msg)
            self.valid_subfolders = []

    def scan_project_media(self, project_path):
        """
        扫描小说项目中的媒体文件

        参数:
            project_path (str): 小说项目路径

        返回:
            dict: 媒体文件信息
        """
        media_info = {
            'video_count': 0,
            'audio_count': 0,
            'subtitle_count': 0,
            'episode_count': 0,
            'has_original': False,
            'has_loop': False,
            'has_media': False,
            'missing_files': []
        }

        try:
            episode_audios = {}
            episode_subtitles = {}

            for file in os.listdir(project_path):
                file_path = os.path.join(project_path, file)
                if os.path.isfile(file_path):
                    file_name = os.path.splitext(file)[0]
                    file_lower = file.lower()

                    if file_lower.endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
                        media_info['video_count'] += 1
                        media_info['has_media'] = True

                        if file_name.lower() in ['original', '原视频']:
                            media_info['has_original'] = True
                        elif file_name.lower() in ['loop', '循环']:
                            media_info['has_loop'] = True

                    elif file_lower.endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac')):
                        media_info['audio_count'] += 1
                        media_info['has_media'] = True

                        # 提取文件名中的集数
                        episode_num = self.extract_episode_number(file_name)
                        if episode_num is not None:
                            episode_audios[episode_num] = file_path

                    elif file_lower.endswith(('.srt', '.ass', '.vtt')):
                        media_info['subtitle_count'] += 1

                        # 提取文件名中的集数
                        episode_num = self.extract_episode_number(file_name)
                        if episode_num is not None:
                            episode_subtitles[episode_num] = file_path

            # 检查文件完整性
            if not media_info['has_loop']:
                media_info['missing_files'].append('循环视频 (loop.mp4)')

            if not episode_audios:
                media_info['missing_files'].append('分集音频 (1.mp3, 2.mp3, ...)')

            # 检查音频和字幕对应关系
            audio_episodes = set(episode_audios.keys())
            subtitle_episodes = set(episode_subtitles.keys())

            media_info['episode_count'] = len(audio_episodes)

            missing_subtitles = audio_episodes - subtitle_episodes
            if missing_subtitles:
                for ep in sorted(missing_subtitles):
                    media_info['missing_files'].append(f'{ep}.srt')

        except Exception:
            pass

        return media_info

    def extract_episode_number(self, filename):
        """
        从文件名中提取集数

        支持的格式：
        - 1, 2, 3... (纯数字)
        - 原视频_1, 西游记_2, abc_3... (前缀_数字)
        - 1_后缀, 2_abc... (数字_后缀)
        - 前缀_1_后缀 (前缀_数字_后缀)

        参数:
            filename (str): 文件名（不含扩展名）

        返回:
            int: 集数，如果没有找到数字则返回None
        """
        import re

        # 查找文件名中的所有数字
        numbers = re.findall(r'\d+', filename)

        if not numbers:
            return None

        # 如果只有一个数字，直接返回
        if len(numbers) == 1:
            return int(numbers[0])

        # 如果有多个数字，优先选择：
        # 1. 文件名开头的数字
        # 2. 下划线后的数字
        # 3. 最后一个数字

        # 检查是否以数字开头
        if re.match(r'^\d+', filename):
            return int(numbers[0])

        # 检查下划线后的数字
        underscore_match = re.search(r'_(\d+)', filename)
        if underscore_match:
            return int(underscore_match.group(1))

        # 返回最后一个数字
        return int(numbers[-1])

    def on_bgm_enable_changed(self):
        """BGM启用状态切换回调"""
        enabled = self.enable_bgm_var.get()

        # 控制所有BGM相关控件的状态
        state = tk.NORMAL if enabled else tk.DISABLED

        self.bgm_entry.config(state=state)
        self.bgm_btn.config(state=state)
        self.bgm_radio1.config(state=state)
        self.bgm_radio2.config(state=state)
        self.bgm_volume_entry.config(state=state)

        # 更新标签颜色
        label_color = "#000000" if enabled else "#999999"
        self.bgm_path_label.config(fg=label_color)
        self.bgm_mode_label.config(fg=label_color)
        self.bgm_volume_label.config(fg=label_color)
        self.bgm_percent_label.config(fg=label_color)

        if enabled:
            self.add_log("BGM功能已启用")
            # 如果启用了BGM，清空路径提示重新选择
            self.bgm_path_var.set("")
            self.on_bgm_mode_changed()  # 更新路径标签
        else:
            self.add_log("BGM功能已禁用")
            self.bgm_path_var.set("")

        # 自动保存配置
        self.save_video_composer_config()

    def on_subtitle_enable_changed(self):
        """字幕启用状态切换回调"""
        enabled = self.enable_subtitle_var.get()

        # 控制所有字幕相关控件的状态
        state = tk.NORMAL if enabled else tk.DISABLED

        self.subtitle_style_entry.config(state=state)
        self.subtitle_style_btn.config(state=state)

        # 更新标签颜色
        label_color = "#000000" if enabled else "#999999"
        self.subtitle_style_label.config(fg=label_color)

        if enabled:
            self.add_log("字幕功能已启用")
        else:
            self.add_log("字幕功能已禁用")

        # 自动保存配置
        self.save_video_composer_config()

    def on_bgm_mode_changed(self):
        """BGM模式切换回调"""
        if not self.enable_bgm_var.get():
            return  # 如果BGM未启用，不处理模式切换

        mode = self.bgm_mode_var.get()
        if mode == "单文件循环":
            self.bgm_path_label.config(text="BGM文件:")
            self.add_log("BGM模式切换为: 单文件循环")
        else:
            self.bgm_path_label.config(text="BGM文件夹:")
            self.add_log("BGM模式切换为: 文件夹随机")

        # 清空当前路径，提示用户重新选择
        self.bgm_path_var.set("")
        self.add_log("请重新选择BGM路径")

        # 自动保存配置
        self.save_video_composer_config()

    def select_bgm_file(self):
        """选择BGM文件或文件夹"""
        if not self.enable_bgm_var.get():
            return  # 如果BGM未启用，不处理

        mode = self.bgm_mode_var.get()

        if mode == "单文件循环":
            # 选择单个文件
            file_path = filedialog.askopenfilename(
                title="选择BGM文件",
                filetypes=[("音频文件", "*.mp3 *.wav *.aac *.m4a *.flac"), ("所有文件", "*.*")]
            )
            if file_path:
                self.bgm_path_var.set(file_path)
                self.add_log(f"选择BGM文件: {os.path.basename(file_path)}")
        else:
            # 选择文件夹
            folder_path = filedialog.askdirectory(title="选择BGM文件夹")
            if folder_path:
                # 检查文件夹中是否有音频文件
                audio_files = []
                try:
                    for file in os.listdir(folder_path):
                        if file.lower().endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac')):
                            audio_files.append(file)

                    if audio_files:
                        self.bgm_path_var.set(folder_path)
                        self.add_log(f"选择BGM文件夹: {os.path.basename(folder_path)} (包含 {len(audio_files)} 个音频文件)")
                    else:
                        self.add_log("警告: 选择的文件夹中没有找到音频文件")
                        messagebox.showwarning("警告", "选择的文件夹中没有找到音频文件")
                except Exception as e:
                    self.add_log(f"读取文件夹失败: {str(e)}")
                    messagebox.showerror("错误", f"读取文件夹失败：{str(e)}")

    def set_subtitle_style(self):
        """设置字幕样式"""
        if not self.enable_subtitle_var.get():
            return  # 如果字幕未启用，不处理

        current_style = self.subtitle_style_var.get()
        self.add_log(f"打开字幕样式设置，当前样式: {current_style}")

        # 打开字幕样式设置对话框
        dialog = SubtitleStyleDialog(self.frame, current_style)
        if dialog.result:
            self.add_log(f"用户设置新样式: {dialog.result}")
            self.subtitle_style_var.set(dialog.result)
            self.add_log(f"字幕样式已更新: {dialog.result}")
            # 自动保存配置
            self.save_video_composer_config()
        else:
            self.add_log("字幕样式设置已关闭，未做更改")

    def on_resolution_enable_changed(self):
        """分辨率启用状态切换回调"""
        enabled = self.custom_resolution_var.get()

        # 控制分辨率相关控件的状态
        state = tk.NORMAL if enabled else tk.DISABLED

        self.width_entry.config(state=state)
        self.height_entry.config(state=state)

        # 更新标签颜色
        label_color = "#000000" if enabled else "#999999"
        self.width_label.config(fg=label_color)
        self.height_label.config(fg=label_color)

        if enabled:
            self.add_log("自定义分辨率已启用")
        else:
            self.add_log("自定义分辨率已禁用，将使用默认分辨率")

        # 自动保存配置
        self.save_video_composer_config()

    def on_compression_enable_changed(self):
        """压缩启用状态切换回调"""
        enabled = self.custom_compression_var.get()

        # 控制压缩相关控件的状态
        state = tk.NORMAL if enabled else tk.DISABLED

        self.first_compression_entry.config(state=state)
        self.other_compression_entry.config(state=state)

        # 更新标签颜色
        label_color = "#000000" if enabled else "#999999"
        self.first_compression_label.config(fg=label_color)
        self.other_compression_label.config(fg=label_color)

        if enabled:
            self.add_log("自定义压缩已启用 - 可分别设置第一集和其他集的码率")
        else:
            self.add_log("自定义压缩已禁用，将保持原视频码率")

        # 自动保存配置
        self.save_video_composer_config()

    def on_video_setting_changed(self, event=None):
        """视频设置变更回调"""
        # 延迟保存，避免频繁保存
        if hasattr(self, '_video_save_timer'):
            self.frame.after_cancel(self._video_save_timer)

        self._video_save_timer = self.frame.after(1000, self.save_video_composer_config)  # 1秒后保存

    def update_ffmpeg_status(self):
        """更新FFmpeg状态显示"""
        try:
            # 检查是否有状态标签
            if not hasattr(self, 'ffmpeg_status_label'):
                return

            ffmpeg_available = self.processor.is_available()

            if ffmpeg_available:
                status_text = "✅ FFmpeg 可用 (高性能视频处理)"
                self.ffmpeg_status_label.config(text=status_text, fg="#28a745")
            else:
                status_text = "❌ FFmpeg 不可用，请检查安装和配置"
                self.ffmpeg_status_label.config(text=status_text, fg="#dc3545")

        except Exception as e:
            if hasattr(self, 'ffmpeg_status_label'):
                self.ffmpeg_status_label.config(text=f"状态检查失败: {e}", fg="#dc3545")

    def init_hardware_status(self):
        """初始化硬件加速状态"""
        try:
            if hasattr(self, 'use_hardware_acceleration_var') and hasattr(self, 'hardware_status_label'):
                enabled = self.use_hardware_acceleration_var.get()

                if enabled:
                    # 如果启用了硬件加速，进行检测
                    self.hardware_status_label.config(text="检测中...", fg="#6c757d")
                    self.detect_hardware_acceleration()
                else:
                    # 如果禁用了硬件加速，显示禁用状态
                    self.hardware_status_label.config(text="已禁用", fg="#dc3545")
        except Exception as e:
            print(f"初始化硬件状态失败: {e}")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = filedialog.askdirectory(title="选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_var.set(dir_path)
            self.add_log(f"设置输出目录: {dir_path}")
            # 保存配置
            self.save_video_composer_config()

    def get_settings(self):
        """获取当前设置"""
        settings = {
            'input_folder': self.input_folder_var.get(),
            'enable_bgm': self.enable_bgm_var.get(),
            'bgm_path': self.bgm_path_var.get() if self.enable_bgm_var.get() else "",
            'bgm_mode': self.bgm_mode_var.get() if self.enable_bgm_var.get() else "单文件循环",
            'bgm_volume': int(self.bgm_volume_var.get()) if self.enable_bgm_var.get() and self.bgm_volume_var.get().isdigit() else 15,
            'enable_subtitle': self.enable_subtitle_var.get(),
            'subtitle_style': self.subtitle_style_var.get() if self.enable_subtitle_var.get() else "",
            'enable_merge': self.enable_merge_var.get(),
            'merge_mode': self.merge_mode_var.get(),
            'enable_parallel': self.enable_parallel_var.get(),
            'max_threads': int(self.max_threads_var.get()) if self.max_threads_var.get().isdigit() else 4,
            'encoding_speed': "ultrafast",  # 强制使用极速模式
            'fast_mode': True,  # 强制启用极速模式
            'speed_priority': self.speed_priority_var.get(),  # 新增：速度优先模式
            'use_hardware_acceleration': self.use_hardware_acceleration_var.get(),
            'custom_resolution': self.custom_resolution_var.get(),
            'width': int(self.width_var.get()) if self.width_var.get().isdigit() else 960,
            'height': int(self.height_var.get()) if self.height_var.get().isdigit() else 720,
            'custom_compression': self.custom_compression_var.get(),
            'first_compression_bitrate': int(self.first_compression_var.get()) if self.custom_compression_var.get() and self.first_compression_var.get().isdigit() else 5000,
            'other_compression_bitrate': int(self.other_compression_var.get()) if self.custom_compression_var.get() and self.other_compression_var.get().isdigit() else 3000,
            'format': self.output_format_var.get(),
            # 分步编码器设置
            'subtitle_encoder': self.subtitle_encoder_var.get(),
            'audio_merge_encoder': self.audio_merge_encoder_var.get(),
            'final_encoder': self.final_encoder_var.get(),
            'encoding_speed': self.encoding_speed_var.get(),
            'use_hardware_acceleration': self.use_hardware_acceleration_var.get()
        }
        return settings

    def start_processing(self):
        """开始处理"""
        settings = self.get_settings()

        if not settings['input_folder']:
            self.add_log("错误: 请选择处理文件夹")
            messagebox.showwarning("警告", "请选择处理文件夹")
            return

        if not self.output_dir:
            self.add_log("错误: 请选择输出目录")
            messagebox.showwarning("警告", "请选择输出目录")
            return

        if not hasattr(self, 'valid_subfolders') or not self.valid_subfolders:
            self.add_log("错误: 没有找到有效的小说项目")
            messagebox.showwarning("警告", "没有找到有效的小说项目，请确保文件夹结构为：视频素材/小说名称/媒体文件")
            return

        # 验证FFmpeg可用性
        if not self.processor.is_available():
            self.add_log("错误: FFmpeg不可用，请检查FFmpeg安装和配置")
            self.add_log("提示: 可以在AI配音模块中点击'选择FFmpeg路径'来配置FFmpeg")
            # 不再显示弹窗错误，只在日志中记录
            return

        self.add_log("将使用 FFmpeg 进行视频合成")

        # 验证BGM设置
        if settings['enable_bgm']:
            if not settings['bgm_path']:
                self.add_log("错误: 已启用BGM但未选择BGM路径")
                messagebox.showwarning("警告", "已启用BGM但未选择BGM路径")
                return

            if not os.path.exists(settings['bgm_path']):
                self.add_log("错误: BGM路径不存在")
                messagebox.showerror("错误", "BGM路径不存在")
                return

        # 验证视频设置
        if settings['custom_compression']:
            first_bitrate = settings['first_compression_bitrate']
            other_bitrate = settings['other_compression_bitrate']

            if first_bitrate < 100 or first_bitrate > 50000:
                self.add_log("错误: 第一集码率必须在100-50000k之间")
                messagebox.showerror("错误", "第一集码率必须在100-50000k之间")
                return

            if other_bitrate < 100 or other_bitrate > 50000:
                self.add_log("错误: 其他集码率必须在100-50000k之间")
                messagebox.showerror("错误", "其他集码率必须在100-50000k之间")
                return

        # 设置处理状态
        self.is_processing = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.processor.should_stop = False

        # 重置进度
        self.reset_progress()

        # 启动计时器
        self.start_timer()

        self.add_log("=" * 50)
        self.add_log("开始新的处理任务")

        # 启动处理线程
        self.processing_thread = threading.Thread(
            target=self._process_folders_thread,
            args=(settings,),
            daemon=True
        )
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        self.add_log("用户请求停止处理")
        self.processor.stop_processing()  # 会自动终止FFmpeg进程
        self.is_processing = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text="已停止，FFmpeg进程已清理", fg="#dc3545")

        # 停止计时器
        self.stop_timer()

    def _process_folders_thread(self, settings):
        """处理文件夹的后台线程"""
        try:
            self.add_log("开始处理小说项目...")
            self.add_log(f"视频素材文件夹: {settings['input_folder']}")
            self.add_log(f"输出目录: {self.output_dir}")

            # 记录合并模式
            if settings['enable_merge']:
                if settings['merge_mode'] == 'create_merged':
                    self.add_log("合并模式: 创建合并视频（将所有片段合并为单个视频）")
                else:
                    self.add_log("合并模式: 合并原视频与第一集（将原视频与1号音频处理视频合并）")
            else:
                self.add_log("合并功能已关闭: 一个音频对应一个视频文件")

            # 记录BGM和字幕设置
            if settings['enable_bgm']:
                self.add_log(f"BGM已启用: {settings['bgm_mode']} - {settings['bgm_path']}")
                self.add_log(f"BGM音量: {settings['bgm_volume']}%")
            else:
                self.add_log("BGM未启用")

            if settings['enable_subtitle']:
                self.add_log(f"字幕已启用: {settings['subtitle_style']}")
            else:
                self.add_log("字幕未启用")

            # 记录性能设置
            if settings['enable_parallel']:
                self.add_log(f"并行处理已启用，最大线程数: {settings['max_threads']}")
            else:
                self.add_log("并行处理未启用")

            # 记录视频设置
            if settings['custom_resolution']:
                self.add_log(f"自定义分辨率: {settings['width']}x{settings['height']}")
            else:
                self.add_log("保持原始分辨率（不调整）")

            if settings['custom_compression']:
                first_bitrate = settings['first_compression_bitrate']
                other_bitrate = settings['other_compression_bitrate']
                self.add_log(f"视频压缩: 第一集{first_bitrate}k, 其他集{other_bitrate}k")
            else:
                self.add_log("不压缩视频: 保持原视频码率")

            def progress_callback(progress_info):
                if self.is_processing:
                    self.update_progress(progress_info)

            # 处理文件夹
            results = self.processor.process_folders(
                settings, self.output_dir, progress_callback
            )

            if not self.is_processing:
                self.add_log("处理被用户取消")
                return

            # 处理完成
            self.is_processing = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)

            # 清理FFmpeg进程
            self.processor.cleanup_on_exit()

            # 停止计时器
            self.stop_timer()

            # 显示结果
            success_count = sum(1 for r in results if r.get('success', False))
            total_count = len(results)

            # 统计生成的视频文件
            total_videos = 0
            individual_videos = 0
            merged_videos = 0

            for r in results:
                if r.get('success', False):
                    # 统计合并视频
                    if 'output_path' in r or 'main_output_path' in r:
                        merged_videos += 1

                    # 统计单独视频（修复字段名）
                    if 'individual_videos' in r:
                        # 合并模式的分集视频
                        individual_videos += len(r['individual_videos'])
                        # 记录单独视频信息
                        novel_name = r.get('novel_name', '未知')
                        self.add_log(f"✅ {novel_name} - 生成了 {len(r['individual_videos'])} 个分集视频:")
                        for video in r['individual_videos']:
                            self.add_log(f"   📹 第{video['episode']:02d}集: {video['name']}")
                    elif 'individual_results' in r:
                        # 独立视频模式的视频文件
                        successful_files = r.get('successful_files', 0)
                        individual_videos += successful_files
                        # 记录独立视频信息
                        novel_name = r.get('novel_name', '未知')
                        self.add_log(f"✅ {novel_name} - 生成了 {successful_files} 个独立视频文件")

            total_videos = merged_videos + individual_videos

            if success_count == total_count:
                if settings['enable_merge']:
                    result_msg = f"全部完成！成功处理 {success_count} 个小说项目，生成 {merged_videos} 个合并视频和 {individual_videos} 个分集视频"
                    detail_msg = f"视频合成完成！\n成功处理 {success_count} 个小说项目\n生成 {merged_videos} 个合并视频\n生成 {individual_videos} 个分集视频"
                else:
                    result_msg = f"全部完成！成功处理 {success_count} 个小说项目，生成 {total_videos} 个视频文件"
                    detail_msg = f"视频合成完成！\n成功处理 {success_count} 个小说项目\n生成 {total_videos} 个视频文件"

                self.status_label.config(text=result_msg, fg="#28a745")
                self.add_log(result_msg)
                messagebox.showinfo("完成", detail_msg)
            else:
                failed_count = total_count - success_count
                if settings['enable_merge']:
                    result_msg = f"部分完成！成功 {success_count} 个，失败 {failed_count} 个，生成 {merged_videos} 个合并视频和 {individual_videos} 个分集视频"
                    detail_msg = f"视频合成部分完成！\n成功：{success_count} 个小说项目\n失败：{failed_count} 个小说项目\n生成：{merged_videos} 个合并视频，{individual_videos} 个分集视频"
                else:
                    result_msg = f"部分完成！成功 {success_count} 个，失败 {failed_count} 个，生成 {total_videos} 个视频文件"
                    detail_msg = f"视频合成部分完成！\n成功：{success_count} 个小说项目\n失败：{failed_count} 个小说项目\n生成：{total_videos} 个视频文件"

                self.status_label.config(text=result_msg, fg="#ffc107")
                self.add_log(result_msg)

                # 记录失败的小说项目
                for r in results:
                    if not r.get('success', False):
                        novel_name = r.get('novel_name', r.get('folder_path', '未知'))
                        self.add_log(f"❌ 失败: {novel_name} - {r.get('error', '未知错误')}")

                messagebox.showwarning("部分完成", detail_msg)

        except Exception as e:
            self.is_processing = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            error_msg = f"处理失败: {str(e)}"
            self.status_label.config(text=error_msg, fg="#dc3545")
            self.add_log(error_msg)
            messagebox.showerror("错误", f"处理失败：{str(e)}")

    def update_progress(self, progress_info):
        """更新进度显示"""
        if isinstance(progress_info, dict):
            if progress_info.get("type") == "file_progress":
                current = progress_info.get("current_file", 0)
                total = progress_info.get("total_files", 0)
                file_name = progress_info.get("file_name", "")
                message = progress_info.get("message", "")

                self.current_file_label.config(text=file_name)
                if message:
                    self.add_log(message)

            elif progress_info.get("type") == "overall_progress":
                completed = progress_info.get("completed", 0)
                total = progress_info.get("total", 0)
                percentage = progress_info.get("percentage", 0)

                self.overall_progress['value'] = percentage
                self.overall_label.config(text=f"{completed}/{total} ({percentage:.1f}%)")

                if completed == total and total > 0:
                    self.add_log(f"处理完成！总共处理了 {total} 个小说项目")

            elif progress_info.get("type") == "video_progress":
                # 视频合成进度
                step = progress_info.get("step", "")
                progress = progress_info.get("progress", 0)
                message = progress_info.get("message", "")

                if step:
                    self.status_label.config(text=f"{step}: {progress:.1f}%")
                if message:
                    self.add_log(message)
        else:
            # 字符串消息
            message = str(progress_info)

            # 检查是否是进度信息
            progress_keywords = [
                "导出进度:", "视频处理进度:", "处理进度:", "视频渲染:", "字幕处理进度:", "合成进度:",
                "分辨率调整:", "最终编码:", "重新编码合并:", "修复合并", "filter合并:", "拼接视频:",
                "重新编码拼接:", "音视频合并:", "字幕添加:", "创建背景视频:", "生成最终视频:"
            ]
            is_progress = any(keyword in message for keyword in progress_keywords)

            if is_progress:
                self.status_label.config(text=message)

                # 提取百分比（支持小数）
                import re
                percent_match = re.search(r'(\d+(?:\.\d+)?)%', message)
                if percent_match:
                    progress_percent = float(percent_match.group(1))
                    # 使用进度条显示
                    self.update_progress_bar(message, progress_percent)
                else:
                    # 没有百分比的进度信息
                    self.update_progress_bar(message)
            else:
                self.status_label.config(text=message)
                self.add_log(message)

    def start_timer(self):
        """启动计时器"""
        import time
        self.start_time = time.time()
        self.timer_running = True
        self.update_timer()

    def stop_timer(self):
        """停止计时器"""
        self.timer_running = False

    def update_timer(self):
        """更新计时器显示"""
        if self.timer_running and self.start_time:
            import time
            elapsed = time.time() - self.start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)

            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.timer_label.config(text=time_str)

            # 每秒更新一次
            self.frame.after(1000, self.update_timer)

    def reset_progress(self):
        """重置进度显示"""
        self.overall_progress['value'] = 0
        self.overall_label.config(text="0/0 (0%)")
        self.current_file_label.config(text="无")
        self.timer_label.config(text="00:00:00")
        self.status_label.config(text="开始处理...", fg="#007bff")

    def on_hardware_acceleration_changed(self):
        """硬件加速状态切换回调"""
        enabled = self.use_hardware_acceleration_var.get()

        if enabled:
            self.add_log("硬件加速已启用")
            if hasattr(self, 'hardware_status_label'):
                self.hardware_status_label.config(text="检测中...", fg="#6c757d")
            # 异步检测硬件编码器
            self.frame.after(100, self.detect_hardware_acceleration)
        else:
            self.add_log("硬件加速已禁用，将使用软件编码")
            if hasattr(self, 'hardware_status_label'):
                self.hardware_status_label.config(text="已禁用", fg="#dc3545")

        # 自动保存配置
        self.save_video_composer_config()

    def on_speed_priority_changed(self):
        """速度优先模式状态切换回调"""
        enabled = self.speed_priority_var.get()

        if enabled:
            self.add_log("🚀 速度优先模式已启用 - 将使用最快编码参数")
            # 自动启用硬件加速（如果可用）
            if not self.use_hardware_acceleration_var.get():
                self.use_hardware_acceleration_var.set(True)
                self.add_log("自动启用硬件加速以获得最佳速度")
        else:
            self.add_log("速度优先模式已禁用 - 将平衡速度和质量")

        # 自动保存配置
        self.save_video_composer_config()

    def auto_performance_optimization(self):
        """自动性能优化（静默运行）"""
        def run_optimization():
            try:
                # 更新UI状态
                self.frame.after(0, lambda: self.optimization_result_label.config(
                    text="🔧 正在进行性能优化...", fg="#007bff"))

                # 获取FFmpeg合成器
                composer = self.processor.get_composer()

                # 运行性能基准测试（静默模式）
                self.frame.after(0, lambda: self.add_log("[性能优化] 开始性能基准测试..."))

                results = composer.run_performance_benchmark(
                    progress_callback=lambda msg: self.frame.after(0, lambda: self.add_log(f"[性能优化] {msg}"))
                )

                if results and isinstance(results, dict):
                    self.frame.after(0, lambda: self.add_log("[性能优化] 基准测试完成"))
                    # 在主线程中应用优化设置
                    self.frame.after(0, lambda: self.apply_auto_optimization(results))
                else:
                    self.frame.after(0, lambda: self.add_log("性能测试未返回编码器信息，使用默认设置"))
                    self.frame.after(0, lambda: self._apply_default_optimization())

            except Exception as e:
                error_msg = str(e)
                self.frame.after(0, lambda: self.add_log(f"[性能优化] 优化过程异常: {error_msg}"))
                self.frame.after(0, lambda: self.optimization_result_label.config(
                    text=f"❌ 优化失败: {error_msg}", fg="#dc3545"))
                # 发生异常时也使用默认优化
                self.frame.after(0, lambda: self._apply_default_optimization())

        # 在后台线程中运行测试
        import threading
        thread = threading.Thread(target=run_optimization, daemon=True)
        thread.start()

    def apply_auto_optimization(self, results):
        """自动应用优化设置（静默）"""
        try:
            # 检查results是否为None
            if results is None:
                self.optimization_result_label.config(
                    text="❌ 性能测试失败", fg="#dc3545"
                )
                self.add_log("性能测试返回空结果，使用默认优化策略")
                # 使用默认优化策略
                self._apply_default_optimization()
                return

            applied_changes = []

            # 应用最快编码器设置
            fastest_encoder_info = results.get('fastest_encoder') if results else None
            if fastest_encoder_info and isinstance(fastest_encoder_info, dict):
                fastest_encoder = fastest_encoder_info.get('encoder')
                fastest_encoder_name = fastest_encoder_info.get('name', fastest_encoder)

                if fastest_encoder:
                    # 更新编码器设置
                    self.subtitle_encoder_var.set(fastest_encoder)
                    self.audio_merge_encoder_var.set(fastest_encoder)
                    self.final_encoder_var.set(fastest_encoder)

                    applied_changes.append(f"编码器: {fastest_encoder_name}")
                else:
                    self.add_log("性能测试未找到可用编码器，使用默认设置")
            else:
                self.add_log("性能测试未返回编码器信息，使用默认设置")

            # 启用硬件加速（如果有硬件编码器可用）
            if fastest_encoder_info and fastest_encoder_info.get('encoder'):
                encoder_name = fastest_encoder_info.get('encoder', '')
                if any(hw in encoder_name for hw in ['nvenc', 'qsv', 'amf']):
                    if not self.use_hardware_acceleration_var.get():
                        self.use_hardware_acceleration_var.set(True)
                        applied_changes.append("硬件加速")

            # 启用速度优先模式
            if not self.speed_priority_var.get():
                self.speed_priority_var.set(True)
                applied_changes.append("速度优先模式")

            # 优化线程数
            import multiprocessing
            cpu_count = multiprocessing.cpu_count()
            optimal_threads = min(cpu_count, 16)
            current_threads = int(self.max_threads_var.get()) if self.max_threads_var.get().isdigit() else 4

            if current_threads < optimal_threads:
                self.max_threads_var.set(str(optimal_threads))
                # 不在applied_changes中显示线程数

            # 标记性能优化已完成
            self._performance_optimized = True

            # 保存配置
            self.save_video_composer_config()

            # 更新状态显示 - 简化显示
            if applied_changes:
                self.optimization_result_label.config(
                    text="✅ 性能优化完成", fg="#28a745"
                )
                self.add_log("性能自动优化完成")
            else:
                self.optimization_result_label.config(
                    text="✅ 配置已是最优", fg="#28a745"
                )
                self.add_log("当前配置已经是最优，无需调整")

        except Exception as e:
            self.optimization_result_label.config(
                text=f"❌ 应用优化失败: {str(e)}", fg="#dc3545"
            )
            self.add_log(f"自动优化失败: {str(e)}")
            # 发生错误时使用默认优化策略
            self._apply_default_optimization()

    def _apply_default_optimization(self):
        """应用默认优化策略（当性能测试失败时使用）"""
        try:
            applied_changes = []

            # 检测可用的硬件编码器
            composer = self.processor.get_composer()
            hardware_encoders = composer._detect_hardware_encoders()

            # 智能选择最优编码器
            if hardware_encoders:
                # 优先使用NVENC，其次QSV，最后AMF
                if 'h264_nvenc' in hardware_encoders:
                    best_encoder = 'h264_nvenc'
                    encoder_name = 'NVIDIA NVENC'
                elif 'h264_qsv' in hardware_encoders:
                    best_encoder = 'h264_qsv'
                    encoder_name = 'Intel QSV'
                elif 'h264_amf' in hardware_encoders:
                    best_encoder = 'h264_amf'
                    encoder_name = 'AMD AMF'
                else:
                    best_encoder = 'libx264'
                    encoder_name = '软件编码'
            else:
                best_encoder = 'libx264'
                encoder_name = '软件编码'

            # 应用最优设置
            self.subtitle_encoder_var.set(best_encoder)
            self.audio_merge_encoder_var.set(best_encoder)
            self.final_encoder_var.set(best_encoder)
            applied_changes.append(f"编码器: {encoder_name}")

            # 启用硬件加速（如果有硬件编码器）
            if best_encoder != 'libx264':
                self.use_hardware_acceleration_var.set(True)
                applied_changes.append("硬件加速")

            # 启用速度优先模式
            if not self.speed_priority_var.get():
                self.speed_priority_var.set(True)
                applied_changes.append("速度优先模式")

            # 优化线程数
            import multiprocessing
            cpu_count = multiprocessing.cpu_count()
            optimal_threads = min(cpu_count, 16)
            current_threads = int(self.max_threads_var.get()) if self.max_threads_var.get().isdigit() else 4

            if current_threads < optimal_threads:
                self.max_threads_var.set(str(optimal_threads))
                # 不在applied_changes中显示线程数

            # 标记性能优化已完成
            self._performance_optimized = True

            # 保存配置
            self.save_video_composer_config()

            # 更新状态显示 - 简化显示
            if applied_changes:
                self.optimization_result_label.config(
                    text="✅ 默认优化完成", fg="#28a745"
                )
                self.add_log("默认优化策略已应用")
            else:
                self.optimization_result_label.config(
                    text="✅ 配置已是最优", fg="#28a745"
                )
                self.add_log("当前配置已经是最优，无需调整")

        except Exception as e:
            # 即使默认优化失败，也标记为已优化，避免重复尝试
            self._performance_optimized = True
            self.save_video_composer_config()

            self.optimization_result_label.config(
                text=f"❌ 默认优化失败: {str(e)}", fg="#dc3545"
            )
            self.add_log(f"默认优化策略失败: {str(e)}")

    def run_performance_optimization(self):
        """运行性能优化测试"""
        def run_optimization():
            try:
                # 更新UI状态
                self.frame.after(0, lambda: self.optimization_result_label.config(
                    text="正在运行性能测试...", fg="#007bff"))

                # 获取FFmpeg合成器
                composer = self.processor.get_composer()

                # 运行性能基准测试
                results = composer.run_performance_benchmark(
                    progress_callback=lambda msg: self.frame.after(0, lambda: self.add_log(f"[性能测试] {msg}"))
                )

                if results:
                    # 在主线程中应用优化设置
                    self.frame.after(0, lambda: self.apply_auto_optimization(results))
                else:
                    self.frame.after(0, lambda: self.optimization_result_label.config(
                        text="性能测试失败", fg="#dc3545"))

            except Exception as e:
                error_msg = str(e)
                self.frame.after(0, lambda: self.optimization_result_label.config(
                    text=f"测试失败: {error_msg}", fg="#dc3545"))

        # 在后台线程中运行测试
        import threading
        thread = threading.Thread(target=run_optimization, daemon=True)
        thread.start()





    def detect_hardware_acceleration(self):
        """检测硬件加速支持"""
        def detect_in_background():
            try:
                composer = self.processor.get_composer()
                hardware_encoders = composer._detect_hardware_encoders()

                # 在主线程中更新UI
                try:
                    self.frame.after(0, lambda: self.update_hardware_status(hardware_encoders))
                except RuntimeError:
                    # 如果主循环未运行，直接调用更新方法
                    self.update_hardware_status(hardware_encoders)

            except Exception as e:
                try:
                    self.frame.after(0, lambda: self.update_hardware_status_error(str(e)))
                except RuntimeError:
                    # 如果主循环未运行，直接调用更新方法
                    self.update_hardware_status_error(str(e))

        # 在后台线程中检测
        import threading
        thread = threading.Thread(target=detect_in_background, daemon=True)
        thread.start()







    def optimize_encoder_settings(self):
        """一键优化编码器设置（简化版）"""
        if self.is_processing:
            messagebox.showwarning("提示", "正在处理中，请等待完成后再优化")
            return

        try:
            # 更新状态
            self.test_result_label.config(text="正在优化编码器设置...", fg="#007bff")

            # 检测可用的硬件编码器
            composer = self.processor.get_composer()
            hardware_encoders = composer._detect_hardware_encoders()

            # 智能选择最优编码器
            if hardware_encoders:
                # 优先使用NVENC，其次QSV，最后AMF
                if 'h264_nvenc' in hardware_encoders:
                    best_encoder = 'h264_nvenc'
                    encoder_name = 'NVIDIA NVENC'
                elif 'h264_qsv' in hardware_encoders:
                    best_encoder = 'h264_qsv'
                    encoder_name = 'Intel QSV'
                elif 'h264_amf' in hardware_encoders:
                    best_encoder = 'h264_amf'
                    encoder_name = 'AMD AMF'
                else:
                    best_encoder = 'libx264'
                    encoder_name = '软件编码'
            else:
                best_encoder = 'libx264'
                encoder_name = '软件编码'

            # 应用最优设置
            self.subtitle_encoder_var.set(best_encoder)
            self.audio_merge_encoder_var.set(best_encoder)
            self.final_encoder_var.set(best_encoder)

            # 启用硬件加速（如果有硬件编码器）
            if best_encoder != 'libx264':
                self.use_hardware_acceleration_var.set(True)

            # 启用速度优先模式
            self.speed_priority_var.set(True)

            # 保存设置
            self.on_video_setting_changed()

            # 更新状态显示
            self.test_result_label.config(
                text=f"✅ 已优化为: {encoder_name}",
                fg="#28a745"
            )

            messagebox.showinfo("优化完成", f"编码器已优化为: {encoder_name}\n已启用速度优先模式")

        except Exception as e:
            self.test_result_label.config(text=f"❌ 优化失败: {str(e)}", fg="#dc3545")
            messagebox.showerror("优化失败", f"优化过程中发生错误:\n{str(e)}")



    def get_optimized_encoder_allocation(self, available_encoders):
        """获取优化的编码器分配方案"""
        # 默认设置
        settings = {
            'subtitle': 'auto',
            'audio_merge': 'auto',
            'final': 'auto'
        }

        if not available_encoders:
            return settings

        # 获取最快的编码器
        fastest_encoder = available_encoders[0][0]

        # 检查是否有硬件编码器
        hardware_encoders = [enc for enc in available_encoders if any(hw in enc[0] for hw in ['nvenc', 'qsv', 'amf'])]
        software_encoders = [enc for enc in available_encoders if enc[0] == 'libx264']

        if hardware_encoders and software_encoders:
            # 有硬件和软件编码器，智能分配
            hw_fastest = hardware_encoders[0][0]
            sw_fastest = software_encoders[0][0]

            # 字幕合成：通常硬件编码器更快
            settings['subtitle'] = hw_fastest

            # 音频合并：如果是简单合并，软件编码器可能更稳定
            # 如果有BGM混音，硬件编码器可能更快
            settings['audio_merge'] = hw_fastest

            # 视频输出：使用最快的编码器
            settings['final'] = fastest_encoder

        elif hardware_encoders:
            # 只有硬件编码器
            settings['subtitle'] = fastest_encoder
            settings['audio_merge'] = fastest_encoder
            settings['final'] = fastest_encoder

        elif software_encoders:
            # 只有软件编码器
            settings['subtitle'] = 'libx264'
            settings['audio_merge'] = 'libx264'
            settings['final'] = 'libx264'

        return settings

    def show_optimization_results(self, settings, available_encoders):
        """显示优化结果"""
        # 创建结果对话框
        dialog = tk.Toplevel(self.frame)
        dialog.title("编码器优化结果")
        dialog.geometry("500x350")
        dialog.resizable(False, False)
        dialog.configure(bg="#f8f9fa")

        # 设置窗口属性
        dialog.transient(self.frame)
        dialog.grab_set()

        # 主框架
        main_frame = tk.Frame(dialog, bg="#f8f9fa", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="编码器优化完成",
                              font=("微软雅黑", 16, "bold"), bg="#f8f9fa", fg="#495057")
        title_label.pack(pady=(0, 15))

        # 优化结果
        result_frame = tk.LabelFrame(main_frame, text="优化设置", font=("微软雅黑", 11, "bold"),
                                   bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        result_frame.pack(fill=tk.X, pady=(0, 15))

        # 显示各步骤的编码器设置
        steps = [
            ("字幕合成", settings['subtitle']),
            ("音频合并", settings['audio_merge']),
            ("视频输出", settings['final'])
        ]

        for step_name, encoder in steps:
            step_frame = tk.Frame(result_frame, bg="#f8f9fa")
            step_frame.pack(fill=tk.X, pady=2)

            tk.Label(step_frame, text=f"{step_name}:", font=("微软雅黑", 10),
                    bg="#f8f9fa", width=10, anchor="w").pack(side=tk.LEFT)

            # 找到编码器的显示名称
            encoder_name = encoder
            for enc_code, _, enc_name in available_encoders:
                if enc_code == encoder:
                    encoder_name = enc_name
                    break

            tk.Label(step_frame, text=encoder_name, font=("微软雅黑", 10, "bold"),
                    bg="#f8f9fa", fg="#28a745").pack(side=tk.LEFT, padx=(10, 0))

        # 性能信息
        perf_frame = tk.LabelFrame(main_frame, text="性能信息", font=("微软雅黑", 11, "bold"),
                                 bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        perf_frame.pack(fill=tk.X, pady=(0, 15))

        for i, (encoder, time_cost, name) in enumerate(available_encoders[:3]):  # 只显示前3个
            perf_item = tk.Frame(perf_frame, bg="#f8f9fa")
            perf_item.pack(fill=tk.X, pady=1)

            rank_color = "#28a745" if i == 0 else "#6c757d"
            rank_text = f"#{i+1}"

            tk.Label(perf_item, text=rank_text, font=("微软雅黑", 9, "bold"),
                    bg="#f8f9fa", fg=rank_color, width=3).pack(side=tk.LEFT)

            tk.Label(perf_item, text=name, font=("微软雅黑", 9),
                    bg="#f8f9fa", width=20, anchor="w").pack(side=tk.LEFT)

            tk.Label(perf_item, text=f"{time_cost:.2f}秒", font=("微软雅黑", 9),
                    bg="#f8f9fa", fg=rank_color).pack(side=tk.LEFT)

        # 按钮
        button_frame = tk.Frame(main_frame, bg="#f8f9fa")
        button_frame.pack(fill=tk.X)

        close_btn = tk.Button(
            button_frame,
            text="确定",
            font=("微软雅黑", 10, "bold"),
            bg="#28a745",
            fg="white",
            activebackground="#218838",
            activeforeground="white",
            relief=tk.FLAT,
            padx=30,
            pady=6,
            cursor="hand2",
            command=dialog.destroy
        )
        close_btn.pack()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 更新状态标签
        self.test_result_label.config(text="✓ 编码器设置已优化", fg="#28a745")

    def show_optimize_error(self, error_msg):
        """显示优化错误"""
        # 恢复按钮
        for widget in self.frame.winfo_children():
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    if hasattr(child, 'winfo_children'):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, tk.Button) and "优化中..." in grandchild.cget('text'):
                                grandchild.config(state=tk.NORMAL, text="一键优化设置")
                                break

        self.test_result_label.config(text=f"优化失败: {error_msg}", fg="#dc3545")
        messagebox.showerror("优化失败", f"编码器优化过程中发生错误:\n{error_msg}")

    def update_quality_indicators(self, *args):
        """更新质量指示器"""
        try:
            # 更新第一集质量指示器
            first_value = int(self.first_compression_var.get()) if self.first_compression_var.get().isdigit() else 5000
            first_quality, first_color = self.get_quality_info(first_value)
            self.first_quality_label.config(text=first_quality, fg=first_color)

            # 更新其他集质量指示器
            other_value = int(self.other_compression_var.get()) if self.other_compression_var.get().isdigit() else 3000
            other_quality, other_color = self.get_quality_info(other_value)
            self.other_quality_label.config(text=other_quality, fg=other_color)

        except Exception:
            pass  # 忽略输入错误

    def get_quality_info(self, value):
        """根据码率值获取质量信息"""
        if value >= 5000:
            return "高质量", "#28a745"  # 绿色
        elif value >= 3000:
            return "中等质量", "#ffc107"  # 黄色
        elif value >= 1500:
            return "较低质量", "#fd7e14"  # 橙色
        else:
            return "低质量", "#dc3545"  # 红色



    def update_hardware_status(self, hardware_encoders):
        """更新硬件加速状态显示"""
        if hardware_encoders:
            # hardware_encoders 是字符串列表
            encoder_names = hardware_encoders
            primary_encoder = encoder_names[0]  # 第一个是优先使用的

            if len(encoder_names) == 1:
                status_text = f"✓ {primary_encoder}"
                color = "#28a745"
            else:
                status_text = f"✓ {primary_encoder} (+{len(encoder_names)-1})"
                color = "#28a745"

            # 检查是否有硬件状态标签
            if hasattr(self, 'hardware_status_label'):
                self.hardware_status_label.config(text=status_text, fg=color)

            self.add_log(f"检测到 {len(encoder_names)} 个硬件编码器")
            self.add_log("将优先使用硬件加速")
        else:
            if hasattr(self, 'hardware_status_label'):
                self.hardware_status_label.config(text="✗ 未检测到", fg="#dc3545")
            self.add_log("未检测到硬件加速，将使用软件编码")

    def update_hardware_status_error(self, error_msg):
        """更新硬件加速检测错误状态"""
        if hasattr(self, 'hardware_status_label'):
            self.hardware_status_label.config(text="检测失败", fg="#dc3545")
        self.add_log(f"硬件编码器检测失败: {error_msg}")


class SubtitleStyleDialog:
    """字幕样式设置对话框"""

    def __init__(self, parent, current_style="微软雅黑 22pt 粗体"):
        self.parent = parent
        self.result = None

        # 获取字体管理器
        try:
            from .font_manager import FontManager
            self.font_manager = FontManager()
        except Exception as e:
            print(f"无法获取字体管理器: {e}")
            self.font_manager = None

        print(f"Debug: 字幕样式对话框初始化，当前样式: '{current_style}'")

        # 解析当前样式
        self.parse_current_style(current_style)

        print(f"Debug: 解析后的样式 - 字体:{self.font_family}, 大小:{self.font_size}, 粗细:{self.font_weight}, 颜色:{self.font_color}")

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("字幕样式设置")
        self.dialog.geometry("520x850")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 绑定关闭事件，点击右上角叉号时保存设置
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_window_close)

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_ui()

        # 等待对话框关闭
        self.dialog.wait_window()

    def parse_current_style(self, style_str):
        """解析当前样式字符串"""
        # 默认值
        self.font_family = "微软雅黑"
        self.font_size = 22
        self.font_weight = "粗体"
        self.font_color = "白色"
        self.outline_color = "黑色"
        self.outline_width = 2
        self.font_spacing = 0  # 新增字体间距，默认为0
        self.position = "底部居中"

        # 解析样式字符串
        if style_str and style_str.strip():
            parts = style_str.split()

            # 解析字体族
            if len(parts) >= 1:
                self.font_family = parts[0]

            # 解析字体大小
            if len(parts) >= 2 and parts[1].endswith('pt'):
                try:
                    self.font_size = int(parts[1].replace('pt', ''))
                except:
                    pass

            # 解析字体粗细
            if len(parts) >= 3:
                weight_map = {"正常": "正常", "粗体": "粗体", "细体": "细体", "bold": "粗体", "normal": "正常"}
                self.font_weight = weight_map.get(parts[2], parts[2])

            # 解析其他属性
            for i, part in enumerate(parts[3:], 3):
                # 解析颜色
                color_map = {
                    "白色": "白色", "黑色": "黑色", "红色": "红色", "绿色": "绿色",
                    "蓝色": "蓝色", "黄色": "黄色", "青色": "青色", "紫色": "紫色"
                }
                if part in color_map:
                    self.font_color = color_map[part]

                # 解析描边信息
                if part.startswith("描边"):
                    # 格式：描边黑色2px
                    desc_info = part[2:]  # 去掉"描边"
                    # 提取颜色和宽度
                    for color in color_map:
                        if desc_info.startswith(color):
                            self.outline_color = color
                            width_str = desc_info[len(color):].replace('px', '')
                            try:
                                self.outline_width = int(width_str)
                            except:
                                pass
                            break

                # 解析字体间距信息
                if part.startswith("间距"):
                    # 格式：间距2px 或 间距-1px
                    spacing_str = part[2:].replace('px', '')
                    try:
                        self.font_spacing = int(spacing_str)
                    except:
                        pass

                # 解析位置
                position_map = {
                    "底部居中": "底部居中", "顶部居中": "顶部居中", "左下角": "左下角",
                    "右下角": "右下角", "左上角": "左上角", "右上角": "右上角", "居中": "居中"
                }
                if part in position_map:
                    self.position = position_map[part]

    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (520 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (850 // 2)
        self.dialog.geometry(f"520x850+{x}+{y}")

    def create_ui(self):
        """创建用户界面"""
        main_frame = tk.Frame(self.dialog, bg="#f8f9fa", padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(main_frame, text="字幕样式设置", font=("微软雅黑", 16, "bold"),
                              bg="#f8f9fa", fg="#495057")
        title_label.pack(pady=(0, 15))

        # 字体设置
        self.create_font_section(main_frame)

        # 颜色设置
        self.create_color_section(main_frame)

        # 描边设置
        self.create_outline_section(main_frame)

        # 位置设置
        self.create_position_section(main_frame)

        # 预览区域
        self.create_preview_section(main_frame)

        # 按钮区域
        self.create_button_section(main_frame)

    def create_font_section(self, parent):
        """创建字体设置区域"""
        font_frame = tk.LabelFrame(parent, text="字体设置", font=("微软雅黑", 12, "bold"),
                                  bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        font_frame.pack(fill=tk.X, pady=(0, 10))

        # 字体族
        family_frame = tk.Frame(font_frame, bg="#f8f9fa")
        family_frame.pack(fill=tk.X, pady=5)

        tk.Label(family_frame, text="字体:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.font_family_var = tk.StringVar(value=self.font_family)

        # 获取可用字体列表
        font_values = ["微软雅黑", "宋体", "黑体", "楷体", "仿宋", "Arial", "Times New Roman"]
        if self.font_manager:
            try:
                available_fonts = self.font_manager.get_all_fonts()
                # 提取字体名称（移除前缀）
                font_names = [name.replace("[系统] ", "").replace("[自定义] ", "")
                             for name in available_fonts.keys()]
                # 合并并去重
                font_values = list(set(font_values + font_names))
                font_values.sort()
            except Exception as e:
                print(f"获取字体列表失败: {e}")

        family_combo = ttk.Combobox(family_frame, textvariable=self.font_family_var, width=20,
                                   values=font_values)
        family_combo.pack(side=tk.LEFT, padx=(10, 5))
        family_combo.bind('<<ComboboxSelected>>', self.update_preview)
        family_combo.bind('<KeyRelease>', self.update_preview)

        # 字体管理按钮
        font_manage_btn = tk.Button(
            family_frame,
            text="字体管理",
            font=("微软雅黑", 9),
            bg="#007bff",
            fg="white",
            activebackground="#0056b3",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=2,
            cursor="hand2",
            command=self.open_font_manager
        )
        font_manage_btn.pack(side=tk.LEFT)

        # 字体大小
        size_frame = tk.Frame(font_frame, bg="#f8f9fa")
        size_frame.pack(fill=tk.X, pady=5)

        tk.Label(size_frame, text="大小:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.font_size_var = tk.StringVar(value=str(self.font_size))
        size_entry = tk.Entry(size_frame, textvariable=self.font_size_var, width=10)
        size_entry.pack(side=tk.LEFT, padx=(10, 5))
        size_entry.bind('<KeyRelease>', self.update_preview)

        tk.Label(size_frame, text="pt", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        # 字体粗细
        weight_frame = tk.Frame(font_frame, bg="#f8f9fa")
        weight_frame.pack(fill=tk.X, pady=5)

        tk.Label(weight_frame, text="粗细:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.font_weight_var = tk.StringVar(value=self.font_weight)
        weight_combo = ttk.Combobox(weight_frame, textvariable=self.font_weight_var, width=15,
                                   values=["正常", "粗体", "细体"])
        weight_combo.pack(side=tk.LEFT, padx=(10, 0))
        weight_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 字体间距
        spacing_frame = tk.Frame(font_frame, bg="#f8f9fa")
        spacing_frame.pack(fill=tk.X, pady=5)

        tk.Label(spacing_frame, text="间距:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.font_spacing_var = tk.StringVar(value=str(self.font_spacing))
        spacing_entry = tk.Entry(spacing_frame, textvariable=self.font_spacing_var, width=10)
        spacing_entry.pack(side=tk.LEFT, padx=(10, 5))
        spacing_entry.bind('<KeyRelease>', self.update_preview)

        tk.Label(spacing_frame, text="px (负值紧缩，正值放宽)", font=("微软雅黑", 9), bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT)

    def open_font_manager(self):
        """打开字体管理器"""
        if not self.font_manager:
            messagebox.showwarning("提示", "字体管理器不可用")
            return

        try:
            current_font = self.font_family_var.get()
            selected_font = show_font_dialog(self.dialog, self.font_manager, current_font)

            if selected_font:
                # 移除前缀标记，只保留字体名称
                clean_font_name = selected_font.replace("[系统] ", "").replace("[自定义] ", "")
                self.font_family_var.set(clean_font_name)
                self.update_preview()

        except Exception as e:
            messagebox.showerror("错误", f"打开字体管理器失败: {e}")

    def show_large_preview(self, event=None):
        """显示放大的预览窗口"""
        try:
            # 创建放大预览窗口
            preview_window = tk.Toplevel(self.dialog)
            preview_window.title("字幕预览 - 放大视图")
            preview_window.geometry("800x600")
            preview_window.resizable(True, True)

            # 居中显示窗口
            preview_window.update_idletasks()
            x = (preview_window.winfo_screenwidth() // 2) - (800 // 2)
            y = (preview_window.winfo_screenheight() // 2) - (600 // 2)
            preview_window.geometry(f"800x600+{x}+{y}")

            # 创建大预览画布
            large_canvas = tk.Canvas(preview_window, bg="#000000", highlightthickness=0)
            large_canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 如果有视频帧，先复制到大画布
            if hasattr(self, 'current_video_frame') and self.current_video_frame:
                # 等待画布初始化
                large_canvas.update_idletasks()
                canvas_width = large_canvas.winfo_width()
                canvas_height = large_canvas.winfo_height()

                if canvas_width > 1 and canvas_height > 1:
                    # 复制并缩放视频帧
                    try:
                        from PIL import Image, ImageTk

                        # 从原始帧重新创建大尺寸图片
                        if hasattr(self, 'current_video_frame_path') and os.path.exists(self.current_video_frame_path):
                            img = Image.open(self.current_video_frame_path)
                        else:
                            # 如果没有原始路径，创建黑色背景
                            img = Image.new('RGB', (canvas_width, canvas_height), color='black')

                        # 缩放到画布大小
                        img_width, img_height = img.size
                        scale_w = canvas_width / img_width
                        scale_h = canvas_height / img_height
                        scale = min(scale_w, scale_h)

                        new_width = int(img_width * scale)
                        new_height = int(img_height * scale)
                        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                        photo = ImageTk.PhotoImage(img)
                        large_canvas.create_image(canvas_width//2, canvas_height//2, image=photo)

                        # 保存引用
                        large_canvas.background_image = photo

                    except Exception as e:
                        print(f"Debug: 放大预览加载背景失败: {e}")
                        # 创建黑色背景
                        large_canvas.create_rectangle(0, 0, canvas_width, canvas_height, fill='black', outline='')

            # 渲染字幕预览
            large_canvas.after(100, lambda: self.render_accurate_subtitle_preview(large_canvas))

            # 添加关闭按钮
            close_btn = tk.Button(preview_window, text="关闭", font=("微软雅黑", 10),
                                 command=preview_window.destroy, bg="#6c757d", fg="white",
                                 activebackground="#5a6268", relief=tk.FLAT, padx=20, pady=5)
            close_btn.pack(pady=10)

        except Exception as e:
            messagebox.showerror("错误", f"显示放大预览失败: {e}")

    def render_real_subtitle_preview(self, canvas, scale=1.0):
        """渲染简化的字幕预览"""
        try:
            # 等待画布初始化
            canvas.update_idletasks()
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas.after(100, lambda: self.render_real_subtitle_preview(canvas, scale))
                return

            # 获取当前字幕样式
            try:
                # 构建样式字符串
                style_str = self.get_current_subtitle_style_string()

                # 简单解析样式
                font_family = self.font_family_var.get()
                font_size = int(self.font_size_var.get())
                font_weight = self.font_weight_var.get()
                font_color = self.font_color_var.get()
                outline_color = self.outline_color_var.get()
                outline_width = int(self.outline_width_var.get())

                print(f"预览使用样式: {style_str}")
                print(f"解析结果: 字体={font_family}, 大小={font_size}, 粗细={font_weight}, 颜色={font_color}, 描边={outline_color} {outline_width}px")

            except Exception as e:
                print(f"解析样式失败，回退到直接获取: {e}")
                # 回退到直接获取
                font_family = self.font_family_var.get()
                font_size = int(self.font_size_var.get())
                font_weight = self.font_weight_var.get()
                font_color = self.font_color_var.get()
                outline_color = self.outline_color_var.get()
                outline_width = int(self.outline_width_var.get())

            position = self.position_var.get()

            # 预览文本 - 显示实际参数信息
            preview_text = f"字幕预览效果 ({font_family} {font_size}pt)\nFFmpeg渲染预览"

            # 使用与实际渲染完全相同的分辨率
            # 获取用户设置的分辨率
            if hasattr(self, 'custom_resolution_var') and self.custom_resolution_var.get():
                # 使用自定义分辨率
                actual_width = int(self.width_var.get()) if self.width_var.get().isdigit() else 1920
                actual_height = int(self.height_var.get()) if self.height_var.get().isdigit() else 1080
            else:
                # 使用默认分辨率 1920x1080
                actual_width = 1920
                actual_height = 1080

            # 计算预览尺寸，保持实际分辨率的比例
            actual_ratio = actual_width / actual_height
            canvas_ratio = canvas_width / canvas_height

            if canvas_ratio > actual_ratio:
                # 画布更宽，以高度为准
                preview_height = canvas_height
                preview_width = int(canvas_height * actual_ratio)
            else:
                # 画布更高，以宽度为准
                preview_width = canvas_width
                preview_height = int(canvas_width / actual_ratio)

            print(f"实际渲染分辨率: {actual_width}x{actual_height}")
            print(f"预览分辨率: {preview_width}x{preview_height}")

            # 使用精确字幕预览（与放大预览一致）
            canvas.after(100, lambda: self.render_accurate_subtitle_preview(canvas))

        except Exception as e:
            print(f"渲染真实字幕预览失败: {e}")
            # 回退到简单预览
            self.render_simple_preview(canvas, scale)

    def create_simple_subtitle_preview(self, canvas, text, font_family, font_size,
                                       font_weight, font_color, outline_color,
                                       outline_width, position, width, height):
        """创建简单的字幕预览"""
        try:
            print("使用简化字幕预览")

            # 不要删除画布内容，保留视频帧
            # 只删除之前的字幕文本
            if hasattr(self, 'canvas_text_ids'):
                for text_id in self.canvas_text_ids:
                    canvas.delete(text_id)
                self.canvas_text_ids.clear()
            else:
                self.canvas_text_ids = []

            # 获取画布尺寸
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas.after(100, lambda: self.create_simple_subtitle_preview(
                    canvas, text, font_family, font_size, font_weight,
                    font_color, outline_color, outline_width, position, width, height))
                return

            # 转换字体粗细
            weight = "bold" if font_weight == "粗体" else "normal"

            # 转换颜色
            color_map = {
                "白色": "white", "黑色": "black", "红色": "red", "绿色": "green",
                "蓝色": "blue", "黄色": "yellow", "青色": "cyan", "紫色": "magenta"
            }
            fg_color = color_map.get(font_color, "white")

            # 计算字体大小（根据画布大小调整）
            scale_factor = min(canvas_width / width, canvas_height / height)
            display_font_size = max(8, int(font_size * scale_factor))

            # 计算文本位置
            if position == "底部居中":
                x = canvas_width // 2
                y = canvas_height - 30
                anchor = "s"
            elif position == "顶部居中":
                x = canvas_width // 2
                y = 30
                anchor = "n"
            elif position == "居中":
                x = canvas_width // 2
                y = canvas_height // 2
                anchor = "center"
            else:  # 默认底部居中
                x = canvas_width // 2
                y = canvas_height - 30
                anchor = "s"

            # 创建字体
            try:
                import tkinter.font as tkFont
                font = tkFont.Font(family=font_family, size=display_font_size, weight=weight)
            except:
                font = ("Arial", display_font_size, weight)

            # 绘制字幕文本（带描边效果）
            if outline_width > 0:
                # 绘制描边
                outline_color_hex = color_map.get(outline_color, "black")
                for dx in [-outline_width, 0, outline_width]:
                    for dy in [-outline_width, 0, outline_width]:
                        if dx != 0 or dy != 0:
                            text_id = canvas.create_text(
                                x + dx, y + dy, text=text, font=font,
                                fill=outline_color_hex, anchor=anchor
                            )
                            self.canvas_text_ids.append(text_id)

            # 绘制主文本
            text_id = canvas.create_text(
                x, y, text=text, font=font,
                fill=fg_color, anchor=anchor
            )
            self.canvas_text_ids.append(text_id)

            print(f"字幕预览已绘制: {text} 在位置 ({x}, {y})")

        except Exception as e:
            print(f"字幕预览失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")

    def render_accurate_subtitle_preview(self, canvas):
        """渲染与FFmpeg完全一致的字幕预览"""
        try:
            print("Debug: 开始渲染精确字幕预览")

            # 获取画布尺寸
            canvas.update_idletasks()
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas.after(100, lambda: self.render_accurate_subtitle_preview(canvas))
                return

            # 获取字幕样式
            font_family = self.font_family_var.get()
            font_size = int(self.font_size_var.get())
            font_weight = self.font_weight_var.get()
            font_color = self.font_color_var.get()
            outline_color = self.outline_color_var.get()
            outline_width = int(self.outline_width_var.get())
            position = self.position_var.get()

            # 获取实际视频分辨率（与FFmpeg渲染一致）
            if hasattr(self, 'custom_resolution_var') and self.custom_resolution_var.get():
                video_width = int(self.width_var.get()) if self.width_var.get().isdigit() else 1920
                video_height = int(self.height_var.get()) if self.height_var.get().isdigit() else 1080
            else:
                video_width = 1920
                video_height = 1080

            print(f"Debug: 视频分辨率: {video_width}x{video_height}")
            print(f"Debug: 画布尺寸: {canvas_width}x{canvas_height}")

            # 计算缩放比例
            scale_x = canvas_width / video_width
            scale_y = canvas_height / video_height
            scale = min(scale_x, scale_y)

            # 计算实际显示区域
            display_width = int(video_width * scale)
            display_height = int(video_height * scale)
            offset_x = (canvas_width - display_width) // 2
            offset_y = (canvas_height - display_height) // 2

            print(f"Debug: 缩放比例: {scale:.3f}")
            print(f"Debug: 显示区域: {display_width}x{display_height}")
            print(f"Debug: 偏移: ({offset_x}, {offset_y})")

            # 清除之前的字幕
            if hasattr(self, 'canvas_text_ids'):
                for text_id in self.canvas_text_ids:
                    canvas.delete(text_id)
                self.canvas_text_ids.clear()
            else:
                self.canvas_text_ids = []

            # 预览文本
            preview_text = f"字幕预览效果\n{font_family} {font_size}pt"

            # 计算字幕位置（基于视频分辨率）
            if position == "底部居中":
                # FFmpeg默认底部边距约为视频高度的8%
                text_x_video = video_width // 2
                text_y_video = video_height - int(video_height * 0.08)
            elif position == "顶部居中":
                text_x_video = video_width // 2
                text_y_video = int(video_height * 0.08)
            elif position == "居中":
                text_x_video = video_width // 2
                text_y_video = video_height // 2
            else:  # 默认底部居中
                text_x_video = video_width // 2
                text_y_video = video_height - int(video_height * 0.08)

            # 转换到画布坐标
            text_x_canvas = offset_x + int(text_x_video * scale)
            text_y_canvas = offset_y + int(text_y_video * scale)

            # 计算字体大小（基于缩放）
            display_font_size = max(8, int(font_size * scale))

            print(f"Debug: 视频坐标: ({text_x_video}, {text_y_video})")
            print(f"Debug: 画布坐标: ({text_x_canvas}, {text_y_canvas})")
            print(f"Debug: 字体大小: {font_size} -> {display_font_size}")

            # 转换字体粗细和颜色
            weight = "bold" if font_weight == "粗体" else "normal"
            color_map = {
                "白色": "white", "黑色": "black", "红色": "red", "绿色": "green",
                "蓝色": "blue", "黄色": "yellow", "青色": "cyan", "紫色": "magenta"
            }
            fg_color = color_map.get(font_color, "white")
            outline_color_name = color_map.get(outline_color, "black")

            # 创建字体
            try:
                import tkinter.font as tkFont
                font = tkFont.Font(family=font_family, size=display_font_size, weight=weight)
            except:
                font = ("Arial", display_font_size, weight)

            # 绘制描边
            if outline_width > 0:
                scaled_outline = max(1, int(outline_width * scale))
                for dx in range(-scaled_outline, scaled_outline + 1):
                    for dy in range(-scaled_outline, scaled_outline + 1):
                        if dx != 0 or dy != 0:
                            text_id = canvas.create_text(
                                text_x_canvas + dx, text_y_canvas + dy,
                                text=preview_text, font=font,
                                fill=outline_color_name, anchor="center"
                            )
                            self.canvas_text_ids.append(text_id)

            # 绘制主文本
            text_id = canvas.create_text(
                text_x_canvas, text_y_canvas,
                text=preview_text, font=font,
                fill=fg_color, anchor="center"
            )
            self.canvas_text_ids.append(text_id)

            print(f"Debug: 精确字幕预览已绘制")

        except Exception as e:
            print(f"Debug: 精确字幕预览失败: {e}")
            import traceback
            print(f"Debug: 详细错误: {traceback.format_exc()}")

    def render_simple_preview(self, canvas, scale=1.0):
        """渲染简单的字幕预览（回退方案）"""
        try:
            canvas.delete("all")

            # 获取画布尺寸
            canvas_width = canvas.winfo_width()
            canvas_height = canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas.after(100, lambda: self.render_simple_preview(canvas, scale))
                return

            # 获取字幕样式 - 与实际渲染保持一致
            font_family = self.font_family_var.get()
            font_size = int(self.font_size_var.get())  # 移除scale乘法，使用实际字体大小
            font_weight = self.font_weight_var.get()
            font_color = self.font_color_var.get()
            outline_color = self.outline_color_var.get()
            outline_width = int(self.outline_width_var.get())  # 移除scale乘法，使用实际描边宽度
            position = self.position_var.get()

            # 根据画布大小调整字体大小，保持相对比例
            # 假设标准画布大小为400x300，根据实际画布大小调整
            base_canvas_width = 400
            scale_factor = canvas_width / base_canvas_width
            display_font_size = max(8, int(font_size * scale_factor))  # 最小8px，避免过小
            display_outline_width = max(1, int(outline_width * scale_factor))  # 最小1px

            # 创建字体
            weight = "bold" if font_weight == "粗体" else "normal"
            font_tuple = (font_family, display_font_size, weight)  # 使用调整后的字体大小

            # 颜色映射
            color_map = {
                "白色": "white", "黑色": "black", "红色": "red", "绿色": "green",
                "蓝色": "blue", "黄色": "yellow", "青色": "cyan", "洋红": "magenta"
            }

            text_color = color_map.get(font_color, "white")
            stroke_color = color_map.get(outline_color, "black")

            # 预览文本
            text = "这是字幕预览效果\nSubtitle Preview Effect"

            # 根据ASS标准位置计算字幕位置
            position_map = {
                "左上角": (canvas_width * 0.1, canvas_height * 0.15),      # ASS对齐7
                "顶部居中": (canvas_width * 0.5, canvas_height * 0.15),    # ASS对齐8
                "右上角": (canvas_width * 0.9, canvas_height * 0.15),      # ASS对齐9
                "左中": (canvas_width * 0.1, canvas_height * 0.5),         # ASS对齐4
                "居中": (canvas_width * 0.5, canvas_height * 0.5),         # ASS对齐5
                "右中": (canvas_width * 0.9, canvas_height * 0.5),         # ASS对齐6
                "左下角": (canvas_width * 0.1, canvas_height * 0.85),      # ASS对齐1
                "底部居中": (canvas_width * 0.5, canvas_height * 0.85),    # ASS对齐2
                "右下角": (canvas_width * 0.9, canvas_height * 0.85)       # ASS对齐3
            }
            x, y = position_map.get(position, (canvas_width * 0.5, canvas_height * 0.85))

            # 绘制描边
            if display_outline_width > 0:
                for dx in range(-display_outline_width, display_outline_width + 1):
                    for dy in range(-display_outline_width, display_outline_width + 1):
                        if dx != 0 or dy != 0:
                            canvas.create_text(
                                x + dx, y + dy, text=text, font=font_tuple,
                                fill=stroke_color, anchor=tk.CENTER, justify=tk.CENTER
                            )

            # 绘制主文本
            canvas.create_text(
                x, y, text=text, font=font_tuple,
                fill=text_color, anchor=tk.CENTER, justify=tk.CENTER
            )

        except Exception as e:
            print(f"简单预览渲染失败: {e}")

    def get_current_subtitle_style_string(self):
        """获取当前字幕样式字符串，与实际渲染保持一致"""
        font_family = self.font_family_var.get()
        font_size = self.font_size_var.get()
        font_weight = self.font_weight_var.get()
        font_color = self.font_color_var.get()
        outline_color = self.outline_color_var.get()
        outline_width = self.outline_width_var.get()
        font_spacing = self.font_spacing_var.get()

        # 构建样式字符串，格式与FFmpeg合成器中的解析保持一致
        weight_str = "粗体" if font_weight == "粗体" else "正常"

        # 修复样式字符串格式，确保描边信息正确
        style_parts = [font_family, f"{font_size}pt", weight_str, font_color]

        # 添加描边信息（如果有描边）
        if outline_width and int(outline_width) > 0:
            style_parts.append(f"描边{outline_color}{outline_width}px")

        # 添加字体间距信息（如果不为0）
        if font_spacing and int(font_spacing) != 0:
            style_parts.append(f"间距{font_spacing}px")

        style_str = " ".join(style_parts)
        return style_str

    def test_preview_accuracy(self):
        """测试预览准确性 - 生成小测试视频进行对比"""
        try:
            import tempfile
            import os
            from tkinter import messagebox

            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            test_video_path = os.path.join(temp_dir, "preview_test.mp4")
            test_srt_path = os.path.join(temp_dir, "test_subtitle.srt")

            # 创建测试字幕文件
            test_subtitle_content = """1
00:00:00,000 --> 00:00:03,000
字幕预览效果测试
Subtitle Preview Test

2
00:00:03,000 --> 00:00:06,000
这是实际渲染效果
This is actual rendering
"""

            with open(test_srt_path, 'w', encoding='utf-8') as f:
                f.write(test_subtitle_content)

            # 获取当前设置
            style_str = self.get_current_subtitle_style_string()

            # 创建测试视频设置
            test_settings = {
                'subtitle_style': style_str,
                'resolution': '1280x720',  # 使用较小分辨率加快生成
                'fps': 30,
                'bitrate': '2M'
            }

            # 询问用户是否继续
            result = messagebox.askyesno(
                "测试预览准确性",
                f"将生成一个6秒的测试视频来验证预览准确性。\n\n"
                f"当前字幕样式: {style_str}\n\n"
                f"测试视频将保存到桌面，是否继续？"
            )

            if not result:
                return

            # 显示进度
            progress_window = tk.Toplevel(self.dialog)
            progress_window.title("生成测试视频")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)

            # 居中显示
            progress_window.update_idletasks()
            x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (progress_window.winfo_screenheight() // 2) - (150 // 2)
            progress_window.geometry(f"400x150+{x}+{y}")

            progress_label = tk.Label(progress_window, text="正在生成测试视频...",
                                    font=("微软雅黑", 12))
            progress_label.pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            progress_window.update()

            # 在后台线程中生成视频
            def generate_test_video():
                try:
                    # 获取FFmpeg路径
                    ffmpeg_exe, ffprobe_exe = self.get_ffmpeg_paths()
                    if not ffmpeg_exe:
                        progress_window.after(0, lambda: show_error("FFmpeg不可用，请检查配置"))
                        return

                    # 创建黑色背景视频
                    progress_window.after(0, lambda: progress_label.config(text="正在创建背景视频..."))

                    # 生成6秒的黑色背景视频
                    bg_video_path = os.path.join(temp_dir, "background.mp4")
                    bg_cmd = [
                        ffmpeg_exe, '-y',
                        '-f', 'lavfi',
                        '-i', 'color=black:size=1280x720:duration=6:rate=30',
                        '-c:v', 'libx264',
                        '-pix_fmt', 'yuv420p',
                        bg_video_path
                    ]

                    import subprocess
                    import platform
                    if platform.system() == "Windows":
                        result = subprocess.run(bg_cmd, capture_output=True, text=True,
                                              encoding='utf-8', errors='ignore', timeout=30,
                                              creationflags=subprocess.CREATE_NO_WINDOW)
                    else:
                        result = subprocess.run(bg_cmd, capture_output=True, text=True,
                                              encoding='utf-8', errors='ignore', timeout=30)

                    if result.returncode != 0:
                        progress_window.after(0, lambda: show_error(f"创建背景视频失败: {result.stderr}"))
                        return

                    # 添加字幕
                    progress_window.after(0, lambda: progress_label.config(text="正在添加字幕..."))

                    # 获取字幕样式
                    font_family = self.font_family_var.get()
                    font_size = self.font_size_var.get()
                    font_color = self.font_color_var.get()
                    outline_color = self.outline_color_var.get()
                    outline_width = self.outline_width_var.get()
                    font_spacing = self.font_spacing_var.get()

                    # 转换颜色格式（从#RRGGBB到&HBBGGRR&）
                    def convert_color_to_ass(color_hex):
                        if color_hex.startswith('#'):
                            color_hex = color_hex[1:]
                        # 转换为BGR格式并添加&H前缀和&后缀
                        r = color_hex[0:2]
                        g = color_hex[2:4]
                        b = color_hex[4:6]
                        return f"&H00{b}{g}{r}&"

                    primary_color = convert_color_to_ass(font_color)
                    outline_color_ass = convert_color_to_ass(outline_color)

                    # 修复路径问题：使用正斜杠并转义特殊字符
                    srt_path_fixed = test_srt_path.replace('\\', '/').replace(':', '\\:')

                    # 构建字幕滤镜（使用正确的ASS样式格式）
                    subtitle_filter = f"subtitles='{srt_path_fixed}':force_style='FontName={font_family},FontSize={font_size},PrimaryColour={primary_color},OutlineColour={outline_color_ass},Outline={outline_width},Bold=0,Italic=0'"

                    # 生成最终视频
                    final_cmd = [
                        ffmpeg_exe, '-y',
                        '-i', bg_video_path,
                        '-vf', subtitle_filter,
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        '-crf', '23',
                        test_video_path
                    ]

                    # 隐藏FFmpeg窗口
                    import platform
                    if platform.system() == "Windows":
                        result = subprocess.run(final_cmd, capture_output=True, text=True,
                                              encoding='utf-8', errors='ignore', timeout=60,
                                              creationflags=subprocess.CREATE_NO_WINDOW)
                    else:
                        result = subprocess.run(final_cmd, capture_output=True, text=True,
                                              encoding='utf-8', errors='ignore', timeout=60)

                    if result.returncode != 0:
                        # 如果字幕滤镜失败，尝试使用drawtext滤镜
                        progress_window.after(0, lambda: progress_label.config(text="尝试备用字幕方案..."))

                        # 使用drawtext滤镜作为备用方案（修复Windows路径和字体问题）
                        # 尝试多个常见字体路径
                        font_paths = [
                            'C\\:/Windows/Fonts/msyh.ttc',  # 微软雅黑
                            'C\\:/Windows/Fonts/arial.ttf',  # Arial
                            'C\\:/Windows/Fonts/simhei.ttf',  # 黑体
                            'C\\:/Windows/Fonts/simsun.ttc'   # 宋体
                        ]

                        # 选择第一个存在的字体
                        selected_font = font_paths[0]  # 默认使用微软雅黑
                        for font_path in font_paths:
                            # 检查字体文件是否存在
                            actual_path = font_path.replace('\\:', ':').replace('/', '\\')
                            if os.path.exists(actual_path):
                                selected_font = font_path
                                break

                        # 简化颜色格式（使用基本颜色名称）
                        color_map = {
                            '#ffffff': 'white', '#000000': 'black', '#ff0000': 'red',
                            '#00ff00': 'green', '#0000ff': 'blue', '#ffff00': 'yellow',
                            '#ff00ff': 'magenta', '#00ffff': 'cyan'
                        }
                        text_color = color_map.get(font_color.lower(), 'white')
                        border_color = color_map.get(outline_color.lower(), 'black')

                        drawtext_filter = f"drawtext=text='字幕预览效果测试':fontfile='{selected_font}':fontsize={font_size}:fontcolor={text_color}:x=(w-text_w)/2:y=h-text_h-50:borderw={outline_width}:bordercolor={border_color}"

                        backup_cmd = [
                            ffmpeg_exe, '-y',
                            '-i', bg_video_path,
                            '-vf', drawtext_filter,
                            '-c:v', 'libx264',
                            '-preset', 'fast',
                            '-crf', '23',
                            test_video_path
                        ]

                        if platform.system() == "Windows":
                            backup_result = subprocess.run(backup_cmd, capture_output=True, text=True,
                                                         encoding='utf-8', errors='ignore', timeout=60,
                                                         creationflags=subprocess.CREATE_NO_WINDOW)
                        else:
                            backup_result = subprocess.run(backup_cmd, capture_output=True, text=True,
                                                         encoding='utf-8', errors='ignore', timeout=60)

                        if backup_result.returncode != 0:
                            progress_window.after(0, lambda: show_error(f"字幕添加失败:\n原始错误: {result.stderr}\n备用方案错误: {backup_result.stderr}"))
                            return

                    # 复制到桌面
                    progress_window.after(0, lambda: progress_label.config(text="正在保存到桌面..."))

                    import shutil
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop", "subtitle_preview_test.mp4")
                    shutil.copy2(test_video_path, desktop_path)

                    progress_window.after(0, lambda: show_success(desktop_path))

                except Exception as e:
                    progress_window.after(0, lambda: show_error(str(e)))

            def show_success(video_path):
                progress_bar.stop()
                progress_window.destroy()

                result = messagebox.showinfo(
                    "测试视频生成成功",
                    f"✅ 测试视频已生成完成！\n\n"
                    f"保存位置: {video_path}\n\n"
                    f"当前字幕样式：\n"
                    f"字体: {self.font_family_var.get()}\n"
                    f"大小: {self.font_size_var.get()}pt\n"
                    f"颜色: {self.font_color_var.get()}\n"
                    f"描边: {self.outline_color_var.get()} {self.outline_width_var.get()}px\n\n"
                    f"请播放视频查看实际字幕效果。"
                )

                # 清理临时文件
                try:
                    os.unlink(test_srt_path)
                    import shutil
                    shutil.rmtree(temp_dir)
                except:
                    pass

            def show_error(error_msg):
                progress_bar.stop()
                progress_window.destroy()
                messagebox.showerror("生成失败", f"测试视频生成失败:\n{error_msg}")

                # 清理临时文件
                try:
                    os.unlink(test_srt_path)
                    import shutil
                    shutil.rmtree(temp_dir)
                except:
                    pass

            # 启动后台线程
            import threading
            thread = threading.Thread(target=generate_test_video)
            thread.daemon = True
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"测试预览准确性失败: {e}")

    def create_color_section(self, parent):
        """创建颜色设置区域"""
        color_frame = tk.LabelFrame(parent, text="颜色设置", font=("微软雅黑", 12, "bold"),
                                   bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        color_frame.pack(fill=tk.X, pady=(0, 10))

        # 字体颜色
        font_color_frame = tk.Frame(color_frame, bg="#f8f9fa")
        font_color_frame.pack(fill=tk.X, pady=5)

        tk.Label(font_color_frame, text="字体颜色:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.font_color_var = tk.StringVar(value=self.font_color)
        font_color_combo = ttk.Combobox(font_color_frame, textvariable=self.font_color_var, width=15,
                                       values=["白色", "黑色", "红色", "绿色", "蓝色", "黄色", "青色", "紫色"])
        font_color_combo.pack(side=tk.LEFT, padx=(10, 0))
        font_color_combo.bind('<<ComboboxSelected>>', self.update_preview)

    def create_outline_section(self, parent):
        """创建描边设置区域"""
        outline_frame = tk.LabelFrame(parent, text="描边设置", font=("微软雅黑", 12, "bold"),
                                     bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        outline_frame.pack(fill=tk.X, pady=(0, 10))

        # 描边颜色
        outline_color_frame = tk.Frame(outline_frame, bg="#f8f9fa")
        outline_color_frame.pack(fill=tk.X, pady=5)

        tk.Label(outline_color_frame, text="描边颜色:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.outline_color_var = tk.StringVar(value=self.outline_color)
        outline_color_combo = ttk.Combobox(outline_color_frame, textvariable=self.outline_color_var, width=15,
                                          values=["黑色", "白色", "红色", "绿色", "蓝色", "黄色", "青色", "紫色"])
        outline_color_combo.pack(side=tk.LEFT, padx=(10, 0))
        outline_color_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 描边宽度
        outline_width_frame = tk.Frame(outline_frame, bg="#f8f9fa")
        outline_width_frame.pack(fill=tk.X, pady=5)

        tk.Label(outline_width_frame, text="描边宽度:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.outline_width_var = tk.StringVar(value=str(self.outline_width))
        outline_width_entry = tk.Entry(outline_width_frame, textvariable=self.outline_width_var, width=10)
        outline_width_entry.pack(side=tk.LEFT, padx=(10, 5))
        outline_width_entry.bind('<KeyRelease>', self.update_preview)

        tk.Label(outline_width_frame, text="像素", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

    def create_position_section(self, parent):
        """创建位置设置区域"""
        position_frame = tk.LabelFrame(parent, text="位置设置", font=("微软雅黑", 12, "bold"),
                                      bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        position_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(position_frame, text="字幕位置:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.position_var = tk.StringVar(value=self.position)
        # 使用ASS标准的9个位置
        position_combo = ttk.Combobox(position_frame, textvariable=self.position_var, width=20,
                                     values=["左上角", "顶部居中", "右上角", "左中", "居中", "右中", "左下角", "底部居中", "右下角"])
        position_combo.pack(side=tk.LEFT, padx=(10, 0))
        position_combo.bind('<<ComboboxSelected>>', self.update_preview)

        # 添加位置说明
        tk.Label(position_frame, text="(对应ASS字幕标准位置)",
                font=("微软雅黑", 8), bg="#f8f9fa", fg="#6c757d").pack(side=tk.LEFT, padx=(10, 0))



    def create_preview_section(self, parent):
        """创建预览区域"""
        preview_frame = tk.LabelFrame(parent, text="预览效果", font=("微软雅黑", 12, "bold"),
                                     bg="#f8f9fa", fg="#495057", padx=10, pady=10)
        preview_frame.pack(fill=tk.X, pady=(0, 20))

        # 视频选择区域
        video_select_frame = tk.Frame(preview_frame, bg="#f8f9fa")
        video_select_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(video_select_frame, text="预览视频:", font=("微软雅黑", 10), bg="#f8f9fa").pack(side=tk.LEFT)

        self.preview_video_var = tk.StringVar()
        video_entry = tk.Entry(video_select_frame, textvariable=self.preview_video_var, font=("微软雅黑", 9))
        video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        video_btn = tk.Button(
            video_select_frame,
            text="选择视频",
            font=("微软雅黑", 9),
            bg="#007bff",
            fg="white",
            activebackground="#0056b3",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=2,
            cursor="hand2",
            command=self.select_preview_video
        )
        video_btn.pack(side=tk.RIGHT, padx=(0, 5))

        # 生成预览视频按钮
        preview_video_btn = tk.Button(
            video_select_frame,
            text="生成预览视频",
            font=("微软雅黑", 9),
            bg="#17a2b8",
            fg="white",
            activebackground="#138496",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=2,
            cursor="hand2",
            command=self.generate_preview_video
        )
        preview_video_btn.pack(side=tk.RIGHT, padx=(0, 5))

        # 刷新预览按钮（保留用于快速预览）
        refresh_btn = tk.Button(
            video_select_frame,
            text="快速预览",
            font=("微软雅黑", 9),
            bg="#28a745",
            fg="white",
            activebackground="#218838",
            activeforeground="white",
            relief=tk.FLAT,
            padx=10,
            pady=2,
            cursor="hand2",
            command=self.refresh_video_frame
        )
        refresh_btn.pack(side=tk.RIGHT)

        # 旧的测试按钮（保留但改名）
        test_btn = tk.Button(
            video_select_frame,
            text="测试预览",
            font=("微软雅黑", 9),
            bg="#ffc107",
            fg="black",
            activebackground="#e0a800",
            activeforeground="black",
            relief=tk.FLAT,
            padx=10,
            pady=2,
            cursor="hand2",
            command=self.test_preview_accuracy
        )
        test_btn.pack(side=tk.RIGHT, padx=(0, 5))

        # 预览背景容器
        self.preview_container = tk.Frame(preview_frame, bg="#f8f9fa", height=200)
        self.preview_container.pack(fill=tk.X, pady=5)
        self.preview_container.pack_propagate(False)  # 保持固定高度

        # 预览画布（用于显示视频帧和字幕）
        self.preview_canvas = tk.Canvas(self.preview_container, bg="#000000", highlightthickness=0, cursor="hand2")
        self.preview_canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定双击事件
        self.preview_canvas.bind("<Double-Button-1>", self.show_large_preview)

        # 初始化画布文本ID列表
        self.canvas_text_ids = []

        # 预览说明
        info_frame = tk.Frame(preview_frame, bg="#f8f9fa")
        info_frame.pack(fill=tk.X, pady=(5, 0))

        info_label = tk.Label(info_frame, text="※ 选择视频文件可以在真实视频背景上预览字幕效果",
                             font=("微软雅黑", 9), bg="#f8f9fa", fg="#6c757d")
        info_label.pack(side=tk.LEFT)

        # 添加双击提示和测试提示
        tips_frame = tk.Frame(info_frame, bg="#f8f9fa")
        tips_frame.pack(side=tk.RIGHT)

        click_tip = tk.Label(tips_frame, text="双击预览图可放大查看",
                            font=("微软雅黑", 9), bg="#f8f9fa", fg="#007bff")
        click_tip.pack(side=tk.RIGHT, padx=(0, 10))

        test_tip = tk.Label(tips_frame, text="点击'测试预览'验证准确性",
                           font=("微软雅黑", 9), bg="#f8f9fa", fg="#ffc107")
        test_tip.pack(side=tk.RIGHT)

        # 初始化预览
        self.current_video_frame = None
        self.update_preview()

    def select_preview_video(self):
        """选择预览视频"""
        file_path = filedialog.askopenfilename(
            title="选择预览视频",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv"), ("所有文件", "*.*")]
        )
        if file_path:
            self.preview_video_var.set(file_path)
            self.extract_video_frame(file_path)

    def refresh_video_frame(self):
        """刷新视频预览帧（重新随机选择时间点）"""
        video_path = self.preview_video_var.get()
        if video_path and os.path.exists(video_path):
            print("Debug: 刷新视频预览帧")
            self.extract_video_frame(video_path)
        else:
            messagebox.showwarning("提示", "请先选择一个视频文件")

    def extract_video_frame(self, video_path):
        """使用FFmpeg提取视频帧"""
        try:
            print(f"Debug: 开始提取视频帧: {video_path}")

            # 获取FFmpeg路径
            ffmpeg_exe, ffprobe_exe = self.get_ffmpeg_paths()
            if not ffmpeg_exe:
                print("Debug: FFmpeg不可用，显示默认预览")
                self.show_default_preview()
                return

            # 获取视频时长
            try:
                duration = self.get_video_duration(video_path, ffprobe_exe)
                if duration <= 0:
                    print("Debug: 无法获取视频时长，显示默认预览")
                    self.show_default_preview()
                    return
            except Exception as e:
                print(f"Debug: 获取视频时长失败: {e}")
                self.show_default_preview()
                return

            # 随机选择一个时间点（避开开头和结尾）
            import random
            start_offset = min(5, duration * 0.1)  # 跳过开头5秒或10%
            end_offset = min(5, duration * 0.1)   # 跳过结尾5秒或10%

            if duration > start_offset + end_offset:
                random_time = random.uniform(start_offset, duration - end_offset)
            else:
                random_time = duration / 2

            print(f"Debug: 视频时长: {duration:.2f}秒，选择时间点: {random_time:.2f}秒")

            # 创建临时文件保存帧
            import tempfile
            temp_frame = tempfile.mktemp(suffix='.jpg')

            # 使用FFmpeg提取帧（添加路径处理）
            cmd = [
                ffmpeg_exe, '-y',
                '-ss', str(random_time),
                '-i', video_path,
                '-vframes', '1',
                '-q:v', '2',  # 高质量
                '-f', 'image2',
                '-update', '1',  # 覆盖输出文件
                temp_frame
            ]

            print(f"Debug: FFmpeg命令: {' '.join(cmd)}")

            import subprocess
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=30,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=30)

            print(f"Debug: FFmpeg返回码: {result.returncode}")
            if result.stderr:
                print(f"Debug: FFmpeg错误输出: {result.stderr}")
            if result.stdout:
                print(f"Debug: FFmpeg标准输出: {result.stdout}")

            if result.returncode == 0 and os.path.exists(temp_frame):
                # 检查文件大小
                file_size = os.path.getsize(temp_frame)
                print(f"Debug: 提取的帧文件大小: {file_size} bytes")

                if file_size > 1000:  # 至少1KB
                    # 保存帧路径供放大预览使用
                    frame_copy = tempfile.mktemp(suffix='.jpg')
                    import shutil
                    shutil.copy2(temp_frame, frame_copy)
                    self.current_video_frame_path = frame_copy

                    # 成功提取帧，加载并显示
                    self.load_video_frame(temp_frame)
                    print(f"Debug: 成功提取视频帧，时间点: {random_time:.2f}秒")
                else:
                    print("Debug: 提取的帧文件太小，可能有问题")
                    self.show_default_preview()

                # 清理临时文件
                try:
                    os.unlink(temp_frame)
                except:
                    pass
            else:
                print(f"Debug: FFmpeg提取帧失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"Debug: 错误详情: {result.stderr}")
                self.show_default_preview()

        except Exception as e:
            print(f"Debug: 提取视频帧时出错: {e}")
            self.show_default_preview()

    def get_video_duration(self, video_path, ffprobe_exe):
        """获取视频时长"""
        try:
            cmd = [
                ffprobe_exe, '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]

            import subprocess
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=10,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                return float(result.stdout.strip())
            else:
                return 0
        except Exception as e:
            print(f"Debug: 获取视频时长失败: {e}")
            return 0

    def load_video_frame(self, frame_path):
        """加载视频帧到预览画布"""
        try:
            print(f"Debug: 开始加载视频帧: {frame_path}")

            # 检查文件是否存在
            if not os.path.exists(frame_path):
                print(f"Debug: 帧文件不存在: {frame_path}")
                self.show_default_preview()
                return

            # 检查文件大小
            file_size = os.path.getsize(frame_path)
            print(f"Debug: 帧文件大小: {file_size} bytes")

            from PIL import Image, ImageTk

            # 打开图片
            print("Debug: 尝试打开图片...")
            img = Image.open(frame_path)
            print(f"Debug: 图片尺寸: {img.size}, 模式: {img.mode}")

            # 获取画布尺寸
            self.preview_canvas.update_idletasks()
            canvas_width = self.preview_canvas.winfo_width()
            canvas_height = self.preview_canvas.winfo_height()
            print(f"Debug: 画布尺寸: {canvas_width}x{canvas_height}")

            if canvas_width <= 1 or canvas_height <= 1:
                # 画布还没初始化，延迟加载
                print("Debug: 画布未初始化，延迟加载")
                self.preview_canvas.after(100, lambda: self.load_video_frame(frame_path))
                return

            # 计算缩放比例，保持宽高比
            img_width, img_height = img.size
            scale_w = canvas_width / img_width
            scale_h = canvas_height / img_height
            scale = min(scale_w, scale_h)

            # 缩放图片
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            print(f"Debug: 缩放图片到: {new_width}x{new_height}")

            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # 转换为PhotoImage
            print("Debug: 转换为PhotoImage...")
            photo = ImageTk.PhotoImage(img)

            # 清除画布并显示图片
            print("Debug: 清除画布并显示图片...")
            self.preview_canvas.delete("all")
            if hasattr(self, 'canvas_text_ids'):
                self.canvas_text_ids.clear()

            # 居中显示图片
            x = canvas_width // 2
            y = canvas_height // 2
            self.preview_canvas.create_image(x, y, image=photo)

            # 保存引用防止被垃圾回收
            self.current_video_frame = photo

            print(f"Debug: 成功加载视频帧 {new_width}x{new_height}")

            # 重新绘制字幕
            self.preview_canvas.after(100, lambda: self.render_accurate_subtitle_preview(self.preview_canvas))

        except Exception as e:
            print(f"Debug: 加载视频帧失败: {e}")
            import traceback
            print(f"Debug: 详细错误信息: {traceback.format_exc()}")
            self.show_default_preview()

    def show_default_preview(self):
        """显示默认预览背景"""
        self.preview_canvas.delete("all")
        self.current_video_frame = None
        self.canvas_text_ids.clear()
        # 显示默认的黑色背景
        self.preview_canvas.configure(bg="#000000")
        # 重新绘制字幕
        self.preview_canvas.after(100, self.update_preview)

    def get_ffmpeg_paths(self):
        """获取ffmpeg和ffprobe路径"""
        try:
            # 尝试从AI配音模块的配置中获取ffmpeg路径
            config_file = get_config_path("config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                if 'ffmpeg_path' in config and config['ffmpeg_path']:
                    ffmpeg_dir = config['ffmpeg_path']
                    ffmpeg_path = os.path.join(ffmpeg_dir, "ffmpeg.exe")
                    ffprobe_path = os.path.join(ffmpeg_dir, "ffprobe.exe")

                    if os.path.exists(ffmpeg_path) and os.path.exists(ffprobe_path):
                        return ffmpeg_path, ffprobe_path

            # 如果配置中没有，尝试系统PATH
            import shutil
            ffmpeg_path = shutil.which("ffmpeg")
            ffprobe_path = shutil.which("ffprobe")

            if ffmpeg_path and ffprobe_path:
                return ffmpeg_path, ffprobe_path

            return None, None

        except Exception as e:
            print(f"Debug: 获取ffmpeg路径失败: {e}")
            return None, None

    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = tk.Frame(parent, bg="#f8f9fa")
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 确定按钮
        ok_btn = tk.Button(
            button_frame,
            text="确定",
            font=("微软雅黑", 12),
            bg="#28a745",
            fg="white",
            activebackground="#218838",
            activeforeground="white",
            relief=tk.FLAT,
            padx=30,
            pady=8,
            cursor="hand2",
            command=self.ok_clicked
        )
        ok_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            font=("微软雅黑", 12),
            bg="#6c757d",
            fg="white",
            activebackground="#545b62",
            activeforeground="white",
            relief=tk.FLAT,
            padx=30,
            pady=8,
            cursor="hand2",
            command=self.cancel_clicked
        )
        cancel_btn.pack(side=tk.RIGHT)

        # 重置按钮
        reset_btn = tk.Button(
            button_frame,
            text="重置",
            font=("微软雅黑", 12),
            bg="#ffc107",
            fg="black",
            activebackground="#e0a800",
            activeforeground="black",
            relief=tk.FLAT,
            padx=30,
            pady=8,
            cursor="hand2",
            command=self.reset_clicked
        )
        reset_btn.pack(side=tk.RIGHT, padx=(0, 10))

    def on_window_close(self):
        """窗口关闭事件处理（点击右上角叉号）"""
        # 点击右上角叉号时，保存当前设置
        try:
            # 验证输入
            font_size_str = self.font_size_var.get().strip()
            outline_width_str = self.outline_width_var.get().strip()
            font_spacing_str = self.font_spacing_var.get().strip()

            # 验证字体间距
            font_spacing_valid = True
            font_spacing = 0
            try:
                font_spacing = int(font_spacing_str) if font_spacing_str else 0
                if not (-20 <= font_spacing <= 20):
                    font_spacing_valid = False
            except ValueError:
                font_spacing_valid = False

            if (font_size_str.isdigit() and outline_width_str.isdigit() and font_spacing_valid):
                font_size = int(font_size_str)
                outline_width = int(outline_width_str)

                if 8 <= font_size <= 100 and 0 <= outline_width <= 10:
                    # 输入有效，保存设置
                    self.result = self.get_style_string()
                    print(f"Debug: 窗口关闭时保存样式: {self.result}")
                else:
                    print("Debug: 输入值超出范围，不保存")
            else:
                print("Debug: 输入值无效，不保存")
        except Exception as e:
            print(f"Debug: 窗口关闭时保存失败: {e}")

        # 关闭对话框
        self.dialog.destroy()

    def update_preview(self, event=None):
        """更新预览效果"""
        try:
            # 直接使用精确渲染方法（与放大预览一致）
            self.render_accurate_subtitle_preview(self.preview_canvas)

        except Exception as e:
            print(f"Debug: 预览更新错误: {e}")
            # 如果精确渲染失败，回退到简单预览
            try:
                self.update_simple_preview()
            except:
                pass  # 忽略预览更新错误

    def update_simple_preview(self):
        """更新简单字幕预览（回退方案）"""
        try:
            # 获取当前设置
            font_family = self.font_family_var.get()
            font_size = int(self.font_size_var.get()) if self.font_size_var.get().isdigit() else 14
            font_weight = self.font_weight_var.get()
            font_color = self.font_color_var.get()
            outline_color = self.outline_color_var.get()
            outline_width = int(self.outline_width_var.get()) if self.outline_width_var.get().isdigit() else 2
            position = self.position_var.get()

            # 转换字体粗细
            weight = "bold" if font_weight == "粗体" else "normal"

            # 转换颜色
            color_map = {
                "白色": "white", "黑色": "black", "红色": "red", "绿色": "green",
                "蓝色": "blue", "黄色": "yellow", "青色": "cyan", "紫色": "magenta"
            }
            fg_color = color_map.get(font_color, "white")
            outline_color_hex = color_map.get(outline_color, "black")

            # 删除之前的所有文本对象
            for text_id in self.canvas_text_ids:
                self.preview_canvas.delete(text_id)
            self.canvas_text_ids.clear()

            # 获取画布尺寸
            canvas_width = self.preview_canvas.winfo_width()
            canvas_height = self.preview_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                # 画布还没有初始化，延迟更新
                self.preview_canvas.after(100, self.update_simple_preview)
                return

            # 根据ASS标准位置计算字幕位置
            position_map = {
                "左上角": (canvas_width * 0.1, canvas_height * 0.15),      # ASS对齐7
                "顶部居中": (canvas_width * 0.5, canvas_height * 0.15),    # ASS对齐8
                "右上角": (canvas_width * 0.9, canvas_height * 0.15),      # ASS对齐9
                "左中": (canvas_width * 0.1, canvas_height * 0.5),         # ASS对齐4
                "居中": (canvas_width * 0.5, canvas_height * 0.5),         # ASS对齐5
                "右中": (canvas_width * 0.9, canvas_height * 0.5),         # ASS对齐6
                "左下角": (canvas_width * 0.1, canvas_height * 0.85),      # ASS对齐1
                "底部居中": (canvas_width * 0.5, canvas_height * 0.85),    # ASS对齐2
                "右下角": (canvas_width * 0.9, canvas_height * 0.85)       # ASS对齐3
            }
            x, y = position_map.get(position, (canvas_width * 0.5, canvas_height * 0.85))

            # 创建字体
            font_tuple = (font_family, font_size, weight)

            # 在画布上绘制字幕文本
            text = "这是字幕预览效果\n支持多行显示"

            # 如果有描边，先绘制描边
            if outline_width > 0:
                for dx in range(-outline_width, outline_width + 1):
                    for dy in range(-outline_width, outline_width + 1):
                        if dx != 0 or dy != 0:
                            outline_id = self.preview_canvas.create_text(
                                x + dx, y + dy, text=text, font=font_tuple,
                                fill=outline_color_hex, anchor=tk.CENTER, justify=tk.CENTER
                            )
                            self.canvas_text_ids.append(outline_id)

            # 绘制主文本
            main_text_id = self.preview_canvas.create_text(
                x, y, text=text, font=font_tuple,
                fill=fg_color, anchor=tk.CENTER, justify=tk.CENTER
            )
            self.canvas_text_ids.append(main_text_id)

        except Exception as e:
            print(f"Debug: 简单预览更新错误: {e}")
            pass  # 忽略预览更新错误

    def generate_preview_video(self):
        """生成预览视频"""
        try:
            # 检查是否选择了预览视频
            preview_video_path = self.preview_video_var.get().strip()
            if not preview_video_path or not os.path.exists(preview_video_path):
                messagebox.showwarning("提示", "请先选择一个预览视频文件")
                return

            # 获取当前字幕样式
            style_str = self.get_current_subtitle_style_string()

            # 获取当前选择的位置
            position = self.position_var.get()

            # 创建输出路径
            import tempfile
            output_dir = os.path.join(os.path.expanduser("~"), "Desktop")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(output_dir, f"字幕预览_{timestamp}.mp4")

            # 询问用户是否继续
            result = messagebox.askyesno(
                "生成预览视频",
                f"将生成一个10秒的预览视频来展示字幕效果。\n\n"
                f"当前字幕样式: {style_str}\n"
                f"位置: {position}\n\n"
                f"预览视频将保存到桌面，是否继续？"
            )

            if not result:
                return

            # 显示进度对话框
            progress_dialog = tk.Toplevel(self.dialog)
            progress_dialog.title("生成预览视频")
            progress_dialog.geometry("400x150")
            progress_dialog.resizable(False, False)
            progress_dialog.transient(self.dialog)
            progress_dialog.grab_set()

            # 居中显示
            progress_dialog.geometry("+%d+%d" % (
                self.dialog.winfo_rootx() + 50,
                self.dialog.winfo_rooty() + 50
            ))

            progress_label = tk.Label(progress_dialog, text="正在生成预览视频...",
                                    font=("微软雅黑", 12))
            progress_label.pack(pady=20)

            progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            def generate_video():
                try:
                    # 获取FFmpeg路径
                    ffmpeg_exe, ffprobe_exe = self.get_ffmpeg_paths()
                    if not ffmpeg_exe:
                        raise Exception("FFmpeg路径未配置，请在设置中配置FFmpeg路径")

                    # 创建ASS字幕文件（更可靠的方式）
                    import tempfile
                    temp_dir = tempfile.mkdtemp()
                    ass_file = os.path.join(temp_dir, "preview_subtitle.ass")

                    # 获取字幕样式
                    font_family = self.font_family_var.get()
                    font_size = int(self.font_size_var.get())
                    font_color = self.font_color_var.get()
                    outline_color = self.outline_color_var.get()
                    outline_width = int(self.outline_width_var.get())
                    font_spacing = self.font_spacing_var.get()

                    # 获取当前选择的位置
                    position = self.position_var.get()

                    # 创建ASS字幕内容
                    self._create_preview_ass_file(
                        ass_file, font_family, font_size, font_color,
                        outline_color, outline_width, font_spacing, position
                    )

                    # 使用ASS字幕文件生成预览视频（只取前10秒）
                    # 修复路径问题：使用正斜杠并转义特殊字符
                    ass_path_fixed = ass_file.replace('\\', '/').replace(':', '\\:')

                    cmd = [
                        ffmpeg_exe, '-y',
                        '-i', preview_video_path,
                        '-ss', '0',  # 从开始位置
                        '-t', '10',  # 只生成10秒
                        '-vf', f"subtitles='{ass_path_fixed}'",  # 使用ASS字幕
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        '-crf', '23',
                        '-c:a', 'aac',  # 重新编码音频确保兼容性
                        '-b:a', '128k',
                        '-movflags', '+faststart',  # 优化播放
                        output_path
                    ]

                    print(f"Debug: 执行FFmpeg命令: {' '.join(cmd)}")
                    print(f"Debug: ASS字幕文件: {ass_file}")

                    import subprocess
                    import platform
                    if platform.system() == "Windows":
                        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                                 creationflags=subprocess.CREATE_NO_WINDOW)
                    else:
                        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                    stdout, stderr = process.communicate()

                    print(f"Debug: FFmpeg返回码: {process.returncode}")
                    print(f"Debug: FFmpeg标准输出: {stdout}")
                    print(f"Debug: FFmpeg错误输出: {stderr}")

                    if process.returncode == 0:
                        # 检查输出文件是否存在且大小合理
                        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # 至少1KB
                            progress_dialog.after(0, lambda: show_success())
                        else:
                            error_msg = f"输出文件生成失败或文件过小\n输出路径: {output_path}"
                            progress_dialog.after(0, lambda: show_error(error_msg))
                    else:
                        error_msg = f"FFmpeg执行失败 (返回码: {process.returncode})\n错误信息: {stderr}"
                        progress_dialog.after(0, lambda: show_error(error_msg))

                except Exception as e:
                    error_msg = str(e)  # 捕获错误消息
                    progress_dialog.after(0, lambda: show_error(error_msg))

            def show_success():
                progress_dialog.destroy()
                messagebox.showinfo(
                    "预览视频生成成功",
                    f"预览视频已保存到桌面:\n{os.path.basename(output_path)}"
                )

                # 询问是否打开视频
                if messagebox.askyesno("打开视频", "是否立即播放预览视频？"):
                    try:
                        import subprocess
                        import platform
                        if platform.system() == "Windows":
                            os.startfile(output_path)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.call(["open", output_path])
                        else:  # Linux
                            subprocess.call(["xdg-open", output_path])
                    except Exception as e:
                        messagebox.showerror("错误", f"无法打开视频: {e}")

            def show_error(error_msg):
                progress_dialog.destroy()
                messagebox.showerror("生成失败", f"预览视频生成失败:\n{error_msg}")

            # 在后台线程中生成视频
            import threading
            thread = threading.Thread(target=generate_video, daemon=True)
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"生成预览视频失败: {e}")

    def _create_preview_ass_file(self, ass_file, font_family, font_size, font_color,
                                outline_color, outline_width, font_spacing, position):
        """创建预览用的ASS字幕文件"""
        try:
            # 颜色转换
            def color_to_ass(color_name):
                color_map = {
                    "白色": "&HFFFFFF&", "黑色": "&H000000&", "红色": "&H0000FF&",
                    "绿色": "&H00FF00&", "蓝色": "&HFF0000&", "黄色": "&H00FFFF&",
                    "青色": "&HFFFF00&", "洋红": "&HFF00FF&"
                }
                return color_map.get(color_name, "&HFFFFFF&")

            primary_color = color_to_ass(font_color)
            outline_color_ass = color_to_ass(outline_color)

            # ASS标准位置映射
            position_map = {
                "左上角": 7,      # 左上对齐
                "顶部居中": 8,    # 顶部居中对齐
                "右上角": 9,      # 右上对齐
                "左中": 4,        # 左侧中部对齐
                "居中": 5,        # 完全居中对齐
                "右中": 6,        # 右侧中部对齐
                "左下角": 1,      # 左下对齐
                "底部居中": 2,    # 底部居中对齐
                "右下角": 3       # 右下对齐
            }

            alignment = position_map.get(position, 2)  # 默认底部居中

            # 使用标准边距（ASS会根据对齐方式自动处理）
            margin_l = 20   # 左边距
            margin_r = 20   # 右边距
            margin_v = 20   # 垂直边距

            # 处理字体间距，确保为数值
            try:
                spacing = int(font_spacing) if font_spacing else 0
            except (ValueError, TypeError):
                spacing = 0

            # 创建ASS文件内容
            ass_content = f"""[Script Info]
Title: 字幕预览
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_family},{font_size},{primary_color},&HFFFFFF&,{outline_color_ass},&H80000000&,1,0,0,0,100,100,{spacing},0,1,{outline_width},0,{alignment},{margin_l},{margin_r},{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:00:03.00,Default,,0,0,0,,字幕预览效果测试
Dialogue: 0,0:00:03.00,0:00:06.00,Default,,0,0,0,,位置: {position}
Dialogue: 0,0:00:06.00,0:00:10.00,Default,,0,0,0,,{font_family} {font_size}pt {font_color} 间距{spacing}px
"""

            # 写入ASS文件
            with open(ass_file, 'w', encoding='utf-8') as f:
                f.write(ass_content)

            print(f"Debug: 创建ASS字幕文件: {ass_file}")
            print(f"Debug: 字幕样式: {font_family} {font_size}pt, 间距: {spacing}px, 位置: {position}")

        except Exception as e:
            print(f"创建ASS字幕文件失败: {e}")
            raise

    def get_current_subtitle_style_string(self):
        """获取当前字幕样式字符串（用于预览视频）"""
        return self.get_style_string()

    def get_style_string(self):
        """生成样式字符串"""
        font_family = self.font_family_var.get()
        font_size = self.font_size_var.get()
        font_weight = self.font_weight_var.get()
        font_color = self.font_color_var.get()
        outline_color = self.outline_color_var.get()
        outline_width = self.outline_width_var.get()
        font_spacing = self.font_spacing_var.get()
        position = self.position_var.get()

        # 构建样式字符串，确保字体颜色总是包含
        style_parts = [font_family, f"{font_size}pt", font_weight, font_color]

        # 添加描边信息（如果有描边）
        if outline_width and int(outline_width) > 0:
            style_parts.append(f"描边{outline_color}{outline_width}px")

        # 添加字体间距信息（如果不为0）
        if font_spacing and int(font_spacing) != 0:
            style_parts.append(f"间距{font_spacing}px")

        # 位置信息
        if position != "底部居中":
            style_parts.append(position)

        return " ".join(style_parts)

    def ok_clicked(self):
        """确定按钮点击"""
        try:
            # 验证输入
            font_size_str = self.font_size_var.get().strip()
            outline_width_str = self.outline_width_var.get().strip()
            font_spacing_str = self.font_spacing_var.get().strip()

            if not font_size_str.isdigit():
                messagebox.showerror("错误", "请输入有效的字体大小")
                return

            if not outline_width_str.isdigit():
                messagebox.showerror("错误", "请输入有效的描边宽度")
                return

            # 验证字体间距（允许负数）
            try:
                font_spacing = int(font_spacing_str) if font_spacing_str else 0
            except ValueError:
                messagebox.showerror("错误", "请输入有效的字体间距（整数）")
                return

            font_size = int(font_size_str)
            outline_width = int(outline_width_str)

            if font_size < 8 or font_size > 100:
                messagebox.showerror("错误", "字体大小必须在8-100之间")
                return

            if outline_width < 0 or outline_width > 10:
                messagebox.showerror("错误", "描边宽度必须在0-10之间")
                return

            if font_spacing < -20 or font_spacing > 20:
                messagebox.showerror("错误", "字体间距必须在-20到20之间")
                return

            # 如果验证通过，关闭对话框
            self.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"设置保存失败: {e}")

    def update_composer_status(self):
        """更新合成器状态显示"""
        try:
            ffmpeg_available = self.processor.is_available()

            if ffmpeg_available:
                status_text = "✅ FFmpeg 可用 (高性能视频处理)"
                self.composer_status_label.config(text=status_text, fg="#28a745")
            else:
                status_text = "❌ FFmpeg 不可用，请检查安装和配置"
                self.composer_status_label.config(text=status_text, fg="#dc3545")

        except Exception as e:
            self.composer_status_label.config(text=f"状态检查失败: {e}", fg="#dc3545")

            # 生成样式字符串
            self.result = self.get_style_string()
            print(f"Debug: 生成的样式字符串: {self.result}")  # 调试信息
            self.dialog.destroy()

        except ValueError as e:
            messagebox.showerror("错误", f"输入验证失败: {str(e)}")
        except Exception as e:
            messagebox.showerror("错误", f"保存样式时出错: {str(e)}")

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()

    def reset_clicked(self):
        """重置按钮点击"""
        # 重置为默认值
        self.font_family_var.set("微软雅黑")
        self.font_size_var.set("22")
        self.font_weight_var.set("粗体")
        self.font_color_var.set("白色")
        self.outline_color_var.set("黑色")
        self.outline_width_var.set("2")
        self.position_var.set("底部居中")

        # 更新预览
        self.update_preview()
