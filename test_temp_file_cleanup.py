#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试临时文件及时清理功能
"""

import os
import sys
import tempfile
import time
import shutil

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.video_composer.core import TempFileManager, VideoComposerProcessor

def test_temp_file_manager():
    """测试临时文件管理器"""
    print("=" * 60)
    print("测试临时文件管理器")
    print("=" * 60)
    
    manager = TempFileManager()
    
    # 测试创建临时文件
    print("\n1. 测试创建临时文件")
    print("-" * 40)
    
    temp_files = []
    for i in range(3):
        temp_file = manager.create_temp_file(prefix=f'test_{i}_', suffix='.mp4')
        temp_files.append(temp_file)
        
        # 创建实际文件
        with open(temp_file, 'w') as f:
            f.write(f"test content {i}")
        
        print(f"创建临时文件: {os.path.basename(temp_file)}")
    
    # 测试创建临时目录
    print("\n2. 测试创建临时目录")
    print("-" * 40)
    
    temp_dirs = []
    for i in range(2):
        temp_dir = manager.create_temp_dir(prefix=f'test_dir_{i}_')
        temp_dirs.append(temp_dir)
        
        # 在目录中创建文件
        test_file = os.path.join(temp_dir, f'test_file_{i}.txt')
        with open(test_file, 'w') as f:
            f.write(f"test dir content {i}")
        
        print(f"创建临时目录: {os.path.basename(temp_dir)}")
    
    # 验证文件存在
    print("\n3. 验证文件存在")
    print("-" * 40)
    
    all_exist = True
    for temp_file in temp_files:
        exists = os.path.exists(temp_file)
        print(f"{os.path.basename(temp_file)}: {'存在' if exists else '不存在'}")
        if not exists:
            all_exist = False
    
    for temp_dir in temp_dirs:
        exists = os.path.exists(temp_dir)
        print(f"{os.path.basename(temp_dir)}: {'存在' if exists else '不存在'}")
        if not exists:
            all_exist = False
    
    if all_exist:
        print("✅ 所有临时文件和目录都存在")
    else:
        print("❌ 部分临时文件或目录不存在")
        return False
    
    # 测试清理功能
    print("\n4. 测试清理功能")
    print("-" * 40)
    
    cleaned_files, cleaned_dirs = manager.cleanup_all()
    print(f"清理了 {cleaned_files} 个文件, {cleaned_dirs} 个目录")
    
    # 验证清理结果
    print("\n5. 验证清理结果")
    print("-" * 40)
    
    all_cleaned = True
    for temp_file in temp_files:
        exists = os.path.exists(temp_file)
        print(f"{os.path.basename(temp_file)}: {'仍存在' if exists else '已清理'}")
        if exists:
            all_cleaned = False
    
    for temp_dir in temp_dirs:
        exists = os.path.exists(temp_dir)
        print(f"{os.path.basename(temp_dir)}: {'仍存在' if exists else '已清理'}")
        if exists:
            all_cleaned = False
    
    if all_cleaned:
        print("✅ 所有临时文件和目录都已清理")
        return True
    else:
        print("❌ 部分临时文件或目录未清理")
        return False

def test_context_manager():
    """测试上下文管理器"""
    print("\n" + "=" * 60)
    print("测试上下文管理器")
    print("=" * 60)
    
    manager = TempFileManager()
    
    # 测试临时文件上下文管理器
    print("\n1. 测试临时文件上下文管理器")
    print("-" * 40)
    
    temp_file_path = None
    
    with manager.temp_file_context(prefix='context_test_', suffix='.mp4') as temp_file:
        temp_file_path = temp_file
        
        # 创建文件内容
        with open(temp_file, 'w') as f:
            f.write("context test content")
        
        print(f"在上下文中创建文件: {os.path.basename(temp_file)}")
        print(f"文件存在: {os.path.exists(temp_file)}")
    
    # 检查文件是否已自动清理
    print(f"退出上下文后文件存在: {os.path.exists(temp_file_path)}")
    
    if not os.path.exists(temp_file_path):
        print("✅ 临时文件上下文管理器工作正常")
    else:
        print("❌ 临时文件上下文管理器未正常清理")
        return False
    
    # 测试临时目录上下文管理器
    print("\n2. 测试临时目录上下文管理器")
    print("-" * 40)
    
    temp_dir_path = None
    
    with manager.temp_dir_context(prefix='context_dir_test_') as temp_dir:
        temp_dir_path = temp_dir
        
        # 在目录中创建文件
        test_file = os.path.join(temp_dir, 'test_file.txt')
        with open(test_file, 'w') as f:
            f.write("context dir test content")
        
        print(f"在上下文中创建目录: {os.path.basename(temp_dir)}")
        print(f"目录存在: {os.path.exists(temp_dir)}")
        print(f"目录中的文件存在: {os.path.exists(test_file)}")
    
    # 检查目录是否已自动清理
    print(f"退出上下文后目录存在: {os.path.exists(temp_dir_path)}")
    
    if not os.path.exists(temp_dir_path):
        print("✅ 临时目录上下文管理器工作正常")
        return True
    else:
        print("❌ 临时目录上下文管理器未正常清理")
        return False

def test_video_processor_integration():
    """测试视频处理器集成"""
    print("\n" + "=" * 60)
    print("测试视频处理器集成")
    print("=" * 60)
    
    try:
        processor = VideoComposerProcessor()
        
        print("✅ 视频处理器创建成功")
        print(f"临时文件管理器已集成: {processor.temp_manager is not None}")
        
        # 测试立即清理功能
        processor.cleanup_temp_files_immediate()
        print("✅ 立即清理功能调用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频处理器测试失败: {e}")
        return False

def monitor_temp_directory():
    """监控临时目录的文件数量"""
    print("\n" + "=" * 60)
    print("监控临时目录")
    print("=" * 60)
    
    temp_dir = tempfile.gettempdir()
    
    # 统计当前临时文件
    temp_files = []
    for root, dirs, files in os.walk(temp_dir):
        for file in files:
            if any(pattern in file for pattern in ['temp_', 'episode_', 'video_compose_']):
                temp_files.append(os.path.join(root, file))
    
    print(f"当前临时目录: {temp_dir}")
    print(f"发现相关临时文件: {len(temp_files)} 个")
    
    if temp_files:
        print("临时文件列表:")
        for temp_file in temp_files[:10]:  # 只显示前10个
            try:
                size = os.path.getsize(temp_file)
                print(f"  {os.path.basename(temp_file)} ({size} bytes)")
            except:
                print(f"  {os.path.basename(temp_file)} (无法获取大小)")
        
        if len(temp_files) > 10:
            print(f"  ... 还有 {len(temp_files) - 10} 个文件")

if __name__ == "__main__":
    try:
        print("🧹 临时文件及时清理功能测试")
        
        # 监控当前临时目录状态
        monitor_temp_directory()
        
        # 测试临时文件管理器
        success1 = test_temp_file_manager()
        
        # 测试上下文管理器
        success2 = test_context_manager()
        
        # 测试视频处理器集成
        success3 = test_video_processor_integration()
        
        # 再次监控临时目录
        monitor_temp_directory()
        
        if success1 and success2 and success3:
            print("\n🎉 所有测试通过！临时文件及时清理功能实现成功")
            print("\n📋 功能特点:")
            print("  ✅ 自动跟踪临时文件和目录")
            print("  ✅ 上下文管理器自动清理")
            print("  ✅ 立即清理功能")
            print("  ✅ 集成到视频处理器")
            print("  ✅ 处理完成后自动清理")
        else:
            print("\n❌ 部分测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
